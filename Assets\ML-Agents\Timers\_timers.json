{"count": 1, "self": 922.7741183999999, "total": 922.8705242, "children": {"InitializeActuators": {"count": 10, "self": 0.0009977, "total": 0.0009977, "children": null}, "InitializeSensors": {"count": 10, "self": 0.0019936999999999997, "total": 0.0019936999999999997, "children": null}, "AgentSendState": {"count": 14269, "self": 0.009733799999999999, "total": 0.009733799999999999, "children": null}, "DecideAction": {"count": 14269, "self": 0.0797157, "total": 0.0797157, "children": null}, "AgentAct": {"count": 14269, "self": 0.0039914, "total": 0.0039914, "children": null}}, "gauges": {}, "metadata": {"timer_format_version": "0.1.0", "start_time_seconds": "**********", "unity_version": "6000.1.6f1", "command_line_arguments": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Unity.exe -projectpath C:\\Squadmate -useHub -hubIPC -cloudEnvironment production -licensingIpc LicenseClient-BCE -hubSessionId ff54f03a-f90d-4071-b7aa-091ab5af0106 -accessToken G7Vh0YgKJJmfLUc5v9gzepYv2-ktbQdv-8j0RpDE7Xo00cf", "communication_protocol_version": "1.5.0", "com.unity.ml-agents_version": "2.0.1", "scene_name": "", "end_time_seconds": "**********"}}