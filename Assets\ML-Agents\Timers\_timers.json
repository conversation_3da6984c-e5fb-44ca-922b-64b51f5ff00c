{"count": 1, "self": 17.29612, "total": 17.366943499999998, "children": {"InitializeActuators": {"count": 10, "self": 0.0009973999999999998, "total": 0.0009973999999999998, "children": null}, "InitializeSensors": {"count": 10, "self": 0.0019941, "total": 0.0019941, "children": null}, "AgentSendState": {"count": 6304, "self": 0.012968299999999999, "total": 0.012968299999999999, "children": null}, "DecideAction": {"count": 6304, "self": 0.0478904, "total": 0.0478904, "children": null}, "AgentAct": {"count": 6304, "self": 0.0059740999999999995, "total": 0.0059740999999999995, "children": null}}, "gauges": {}, "metadata": {"timer_format_version": "0.1.0", "start_time_seconds": "**********", "unity_version": "6000.1.6f1", "command_line_arguments": "C:\\Program Files\\Unity\\Hub\\Editor\\6000.1.6f1\\Editor\\Unity.exe -projectpath C:\\Squadmate -useHub -hubIPC -cloudEnvironment production -licensingIpc LicenseClient-BCE -hubSessionId ff54f03a-f90d-4071-b7aa-091ab5af0106 -accessToken G7Vh0YgKJJmfLUc5v9gzepYv2-ktbQdv-8j0RpDE7Xo00cf", "communication_protocol_version": "1.5.0", "com.unity.ml-agents_version": "2.0.1", "scene_name": "", "end_time_seconds": "**********"}}