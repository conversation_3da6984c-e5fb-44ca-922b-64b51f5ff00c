using UnityEngine;
using UnityEditor;

/// <summary>
/// 🧪 PUBG TDM Quick Test - Verifies system compilation and basic functionality
/// </summary>
public class PUBGTDMQuickTest : EditorWindow
{
    [MenuItem("SquadMate AI/🧪 PUBG TDM Quick Test")]
    public static void ShowWindow()
    {
        PUBGTDMQuickTest window = GetWindow<PUBGTDMQuickTest>("PUBG TDM Test");
        window.minSize = new Vector2(400, 300);
        window.Show();
    }
    
    void OnGUI()
    {
        GUILayout.Label("🧪 PUBG TDM System Test", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        EditorGUILayout.HelpBox("This tool tests the PUBG TDM system compilation and basic functionality.", MessageType.Info);
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("🔧 Test System Compilation", GUILayout.Height(30)))
        {
            TestSystemCompilation();
        }
        
        if (GUILayout.Button("🎮 Create Test Arena", GUILayout.Height(30)))
        {
            CreateTestArena();
        }
        
        if (GUILayout.Button("🤖 Test Agent Creation", GUILayout.Height(30)))
        {
            TestAgentCreation();
        }
        
        if (GUILayout.Button("📊 Test Recording System", GUILayout.Height(30)))
        {
            TestRecordingSystem();
        }
    }
    
    void TestSystemCompilation()
    {
        Debug.Log("🧪 Testing PUBG TDM system compilation...");
        
        try
        {
            // Test data structures
            var config = new PUBGTDMConfig();
            var gameMode = new GameModeConfig();
            var weaponData = new WeaponData();
            
            Debug.Log("✅ Data structures compile successfully");
            
            // Test enums
            PUBGTeam team = PUBGTeam.TeamA;
            PUBGRole role = PUBGRole.Assault;
            MovementState movement = MovementState.Running;
            
            Debug.Log("✅ Enums compile successfully");
            
            Debug.Log("✅ PUBG TDM system compilation test passed!");
            
            EditorUtility.DisplayDialog("Compilation Test", 
                "✅ PUBG TDM system compiles successfully!\n\n" +
                "All classes, enums, and data structures are working correctly.", "OK");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Compilation test failed: {e.Message}");
            EditorUtility.DisplayDialog("Compilation Test Failed", 
                $"❌ Error: {e.Message}", "OK");
        }
    }
    
    void CreateTestArena()
    {
        Debug.Log("🎮 Creating test PUBG TDM arena...");
        
        try
        {
            // Create game manager
            GameObject gameManagerObj = new GameObject("PUBG_TDM_Manager_Test");
            PUBGTDMManager manager = gameManagerObj.AddComponent<PUBGTDMManager>();
            
            // Add recorder
            PUBGMatchRecorder recorder = gameManagerObj.AddComponent<PUBGMatchRecorder>();
            
            // Create simple floor
            GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
            floor.name = "Test_Arena_Floor";
            floor.transform.localScale = new Vector3(10, 1, 8);
            
            Debug.Log("✅ Test arena created successfully");
            
            EditorUtility.DisplayDialog("Test Arena Created", 
                "✅ PUBG TDM test arena created!\n\n" +
                "Components added:\n" +
                "• PUBGTDMManager\n" +
                "• PUBGMatchRecorder\n" +
                "• Basic arena floor", "OK");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Arena creation failed: {e.Message}");
        }
    }
    
    void TestAgentCreation()
    {
        Debug.Log("🤖 Testing PUBG agent creation...");
        
        try
        {
            // Create test agent
            GameObject agentObj = new GameObject("Test_PUBG_Agent");
            PUBGAgent agent = agentObj.AddComponent<PUBGAgent>();
            
            // Add required components
            agentObj.AddComponent<UnityEngine.AI.NavMeshAgent>();
            agentObj.AddComponent<Rigidbody>();
            agentObj.AddComponent<CapsuleCollider>();
            
            // Test agent properties
            agent.team = PUBGTeam.TeamA;
            agent.role = PUBGRole.Assault;
            agent.currentWeapon = "M416";
            agent.health = 100f;
            
            Debug.Log("✅ PUBG agent created successfully");
            
            EditorUtility.DisplayDialog("Agent Test", 
                "✅ PUBG agent creation successful!\n\n" +
                "Agent configured with:\n" +
                "• Team A assignment\n" +
                "• Assault role\n" +
                "• M416 weapon\n" +
                "• Full health", "OK");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Agent creation failed: {e.Message}");
        }
    }
    
    void TestRecordingSystem()
    {
        Debug.Log("📊 Testing recording system...");
        
        try
        {
            // Find or create recorder
            PUBGMatchRecorder recorder = FindObjectOfType<PUBGMatchRecorder>();
            if (recorder == null)
            {
                GameObject recorderObj = new GameObject("Test_Recorder");
                recorder = recorderObj.AddComponent<PUBGMatchRecorder>();
            }
            
            // Test decision logging
            recorder.LogDecision("TestAgent", "Attack", "Enemy spotted", 0.8f);
            
            Debug.Log("✅ Recording system test passed");
            
            EditorUtility.DisplayDialog("Recording Test", 
                "✅ Recording system working!\n\n" +
                "Successfully tested:\n" +
                "• Decision logging\n" +
                "• Component creation\n" +
                "• Basic functionality", "OK");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"❌ Recording test failed: {e.Message}");
        }
    }
}
