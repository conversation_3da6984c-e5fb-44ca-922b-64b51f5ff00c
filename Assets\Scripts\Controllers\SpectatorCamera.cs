using UnityEngine;
using System.Collections.Generic;
using System.Linq;

/// <summary>
/// 👁️ Spectator Camera System for 5v5 TDM Matches
/// Provides multiple camera modes for watching AI agent battles
/// </summary>
public class SpectatorCamera : MonoBehaviour
{
    [Header("📹 Camera Settings")]
    public float moveSpeed = 10f;
    public float rotationSpeed = 2f;
    public float zoomSpeed = 5f;
    public float minZoom = 5f;
    public float maxZoom = 50f;

    [Header("🎯 Follow Settings")]
    public float followHeight = 8f;
    public float followDistance = 10f;
    public float followSmoothness = 2f;
    public bool autoSwitchTargets = true;
    public float autoSwitchInterval = 10f;

    [Header("🗺️ Overview Settings")]
    public Vector3 overviewPosition = new Vector3(0, 30, 0);
    public Vector3 overviewRotation = new Vector3(90, 0, 0);
    public float overviewFOV = 60f;

    [Header("⚔️ Combat Focus")]
    public float combatDetectionRange = 15f;
    public bool prioritizeCombat = true;
    public float combatFocusTime = 5f;

    public enum CameraMode
    {
        FreeCamera,
        FollowAgent,
        Overview,
        CombatFocus,
        Cinematic
    }

    [Header("🎮 Controls")]
    public KeyCode freeCameraKey = KeyCode.F1;
    public KeyCode overviewKey = KeyCode.F2;
    public KeyCode followKey = KeyCode.F3;
    public KeyCode combatFocusKey = KeyCode.F4;
    public KeyCode cinematicKey = KeyCode.F5;
    public KeyCode nextAgentKey = KeyCode.Tab;
    public KeyCode previousAgentKey = KeyCode.LeftShift;

    private CameraMode currentMode = CameraMode.Overview;
    private Transform currentTarget;
    private List<Transform> allAgents = new List<Transform>();
    private int currentAgentIndex = 0;
    private float lastAutoSwitchTime;
    private float lastCombatFocusTime;
    private Camera spectatorCam;
    private Vector3 originalPosition;
    private Quaternion originalRotation;

    // Combat tracking
    private List<Transform> agentsInCombat = new List<Transform>();
    private Transform lastCombatTarget;

    void Start()
    {
        spectatorCam = GetComponent<Camera>();
        if (spectatorCam == null)
        {
            spectatorCam = gameObject.AddComponent<Camera>();
        }

        originalPosition = transform.position;
        originalRotation = transform.rotation;

        // Find all agents in the scene
        RefreshAgentList();

        // Start in overview mode
        SetCameraMode(CameraMode.Overview);

        Debug.Log("👁️ Spectator Camera initialized - Use F1-F5 to switch modes, Tab to cycle agents");
        PrintControls();
    }

    void Update()
    {
        HandleInput();
        UpdateCameraMode();

        if (autoSwitchTargets && currentMode == CameraMode.FollowAgent)
        {
            CheckAutoSwitch();
        }

        if (prioritizeCombat)
        {
            CheckCombatFocus();
        }
    }

    void HandleInput()
    {
        // Camera mode switching
        if (Input.GetKeyDown(freeCameraKey))
            SetCameraMode(CameraMode.FreeCamera);
        else if (Input.GetKeyDown(overviewKey))
            SetCameraMode(CameraMode.Overview);
        else if (Input.GetKeyDown(followKey))
            SetCameraMode(CameraMode.FollowAgent);
        else if (Input.GetKeyDown(combatFocusKey))
            SetCameraMode(CameraMode.CombatFocus);
        else if (Input.GetKeyDown(cinematicKey))
            SetCameraMode(CameraMode.Cinematic);

        // Agent switching
        if (Input.GetKeyDown(nextAgentKey))
            SwitchToNextAgent();
        else if (Input.GetKeyDown(previousAgentKey))
            SwitchToPreviousAgent();

        // Mouse scroll for zoom
        float scroll = Input.GetAxis("Mouse ScrollWheel");
        if (scroll != 0f)
        {
            AdjustZoom(scroll);
        }
    }

    void UpdateCameraMode()
    {
        switch (currentMode)
        {
            case CameraMode.FreeCamera:
                UpdateFreeCamera();
                break;
            case CameraMode.FollowAgent:
                UpdateFollowCamera();
                break;
            case CameraMode.Overview:
                UpdateOverviewCamera();
                break;
            case CameraMode.CombatFocus:
                UpdateCombatFocus();
                break;
            case CameraMode.Cinematic:
                UpdateCinematicCamera();
                break;
        }
    }

    void UpdateFreeCamera()
    {
        // WASD movement
        Vector3 movement = Vector3.zero;
        if (Input.GetKey(KeyCode.W)) movement += transform.forward;
        if (Input.GetKey(KeyCode.S)) movement -= transform.forward;
        if (Input.GetKey(KeyCode.A)) movement -= transform.right;
        if (Input.GetKey(KeyCode.D)) movement += transform.right;
        if (Input.GetKey(KeyCode.Q)) movement -= transform.up;
        if (Input.GetKey(KeyCode.E)) movement += transform.up;

        transform.position += movement * moveSpeed * Time.deltaTime;

        // Mouse look (hold right click)
        if (Input.GetMouseButton(1))
        {
            float mouseX = Input.GetAxis("Mouse X") * rotationSpeed;
            float mouseY = Input.GetAxis("Mouse Y") * rotationSpeed;

            transform.Rotate(-mouseY, mouseX, 0);
        }
    }

    void UpdateFollowCamera()
    {
        if (currentTarget == null)
        {
            SwitchToNextAgent();
            return;
        }

        Vector3 targetPosition = currentTarget.position + Vector3.up * followHeight - currentTarget.forward * followDistance;
        transform.position = Vector3.Lerp(transform.position, targetPosition, followSmoothness * Time.deltaTime);

        Vector3 lookDirection = currentTarget.position - transform.position;
        Quaternion targetRotation = Quaternion.LookRotation(lookDirection);
        transform.rotation = Quaternion.Lerp(transform.rotation, targetRotation, followSmoothness * Time.deltaTime);
    }

    void UpdateOverviewCamera()
    {
        transform.position = Vector3.Lerp(transform.position, overviewPosition, 2f * Time.deltaTime);
        transform.rotation = Quaternion.Lerp(transform.rotation, Quaternion.Euler(overviewRotation), 2f * Time.deltaTime);
        spectatorCam.fieldOfView = Mathf.Lerp(spectatorCam.fieldOfView, overviewFOV, 2f * Time.deltaTime);
    }

    void UpdateCombatFocus()
    {
        Transform combatTarget = FindBestCombatTarget();
        if (combatTarget != null)
        {
            currentTarget = combatTarget;
            UpdateFollowCamera();
        }
        else
        {
            // No combat found, switch to overview
            SetCameraMode(CameraMode.Overview);
        }
    }

    void UpdateCinematicCamera()
    {
        // Smooth cinematic movement between interesting points
        if (currentTarget != null)
        {
            // Create cinematic angles and movements
            float time = Time.time * 0.5f;
            Vector3 offset = new Vector3(Mathf.Sin(time) * 8f, 5f, Mathf.Cos(time) * 8f);
            Vector3 targetPos = currentTarget.position + offset;

            transform.position = Vector3.Lerp(transform.position, targetPos, Time.deltaTime);
            transform.LookAt(currentTarget.position + Vector3.up * 1.5f);
        }
    }

    void SetCameraMode(CameraMode mode)
    {
        currentMode = mode;
        Debug.Log($"📹 Camera Mode: {mode}");

        switch (mode)
        {
            case CameraMode.FollowAgent:
                if (currentTarget == null)
                    SwitchToNextAgent();
                break;
            case CameraMode.CombatFocus:
                FindBestCombatTarget();
                break;
        }
    }

    void RefreshAgentList()
    {
        allAgents.Clear();

        // Find VictorAgents
        VictorAgent[] victorAgents = FindObjectsOfType<VictorAgent>();
        foreach (var agent in victorAgents)
        {
            allAgents.Add(agent.transform);
        }

        // Find SquadMateAgents
        SquadMateAgent[] squadAgents = FindObjectsOfType<SquadMateAgent>();
        foreach (var agent in squadAgents)
        {
            allAgents.Add(agent.transform);
        }

        Debug.Log($"📝 Found {allAgents.Count} agents to spectate");
    }

    void SwitchToNextAgent()
    {
        if (allAgents.Count == 0)
        {
            RefreshAgentList();
            return;
        }

        currentAgentIndex = (currentAgentIndex + 1) % allAgents.Count;
        currentTarget = allAgents[currentAgentIndex];

        if (currentTarget != null)
        {
            Debug.Log($"👁️ Following: {currentTarget.name}");
        }
    }

    void SwitchToPreviousAgent()
    {
        if (allAgents.Count == 0) return;

        currentAgentIndex = (currentAgentIndex - 1 + allAgents.Count) % allAgents.Count;
        currentTarget = allAgents[currentAgentIndex];

        if (currentTarget != null)
        {
            Debug.Log($"👁️ Following: {currentTarget.name}");
        }
    }

    void CheckAutoSwitch()
    {
        if (Time.time - lastAutoSwitchTime > autoSwitchInterval)
        {
            SwitchToNextAgent();
            lastAutoSwitchTime = Time.time;
        }
    }

    void CheckCombatFocus()
    {
        if (Time.time - lastCombatFocusTime > 1f) // Check every second
        {
            UpdateCombatList();
            lastCombatFocusTime = Time.time;

            if (agentsInCombat.Count > 0 && currentMode != CameraMode.CombatFocus)
            {
                SetCameraMode(CameraMode.CombatFocus);
            }
        }
    }

    void UpdateCombatList()
    {
        agentsInCombat.Clear();

        foreach (var agent in allAgents)
        {
            if (agent == null) continue;

            VictorAgent victorAgent = agent.GetComponent<VictorAgent>();
            if (victorAgent != null && IsAgentInCombat(victorAgent))
            {
                agentsInCombat.Add(agent);
            }
        }
    }

    bool IsAgentInCombat(VictorAgent agent)
    {
        // Check if agent is in combat based on public properties
        return agent.isInCombat ||
               (Time.time - agent.lastDamageTime < 5f) || // Recently took damage
               agent.currentHealth < agent.maxHealth * 0.8f; // Low health indicates recent combat
    }

    Transform FindBestCombatTarget()
    {
        UpdateCombatList();

        if (agentsInCombat.Count > 0)
        {
            // Return the agent with the most recent combat activity
            return agentsInCombat[0];
        }

        return null;
    }

    void AdjustZoom(float scrollDelta)
    {
        if (currentMode == CameraMode.FreeCamera || currentMode == CameraMode.Overview)
        {
            spectatorCam.fieldOfView = Mathf.Clamp(spectatorCam.fieldOfView - scrollDelta * zoomSpeed, minZoom, maxZoom);
        }
        else if (currentMode == CameraMode.FollowAgent)
        {
            followDistance = Mathf.Clamp(followDistance - scrollDelta * 5f, 3f, 20f);
        }
    }

    void PrintControls()
    {
        Debug.Log("🎮 SPECTATOR CAMERA CONTROLS:");
        Debug.Log("F1 - Free Camera | F2 - Overview | F3 - Follow Agent | F4 - Combat Focus | F5 - Cinematic");
        Debug.Log("Tab - Next Agent | Shift - Previous Agent | Mouse Wheel - Zoom");
        Debug.Log("Free Camera: WASD + QE to move, Right Click + Mouse to look");
    }

    // Public methods for UI integration
    public void SetModeFromUI(int modeIndex)
    {
        SetCameraMode((CameraMode)modeIndex);
    }

    public void NextAgentFromUI()
    {
        SwitchToNextAgent();
    }

    public string GetCurrentModeText()
    {
        return $"Camera: {currentMode}" + (currentTarget != null ? $" - {currentTarget.name}" : "");
    }
}
