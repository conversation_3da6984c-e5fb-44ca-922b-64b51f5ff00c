using UnityEngine;
using Unity.MLAgents;
using Unity.MLAgents.Sensors;
using Unity.MLAgents.Actuators;
using System.Collections.Generic;

/// <summary>
/// PUBG Mobile TDM AI Agent - Authentic PUBG gameplay behaviors
/// Implements realistic movement, combat, and tactical decision making
/// </summary>
public class PUBGAgent : Agent
{
    [Header("🎮 PUBG Agent Configuration")]
    public PUBGTeam team;
    public PUBGRole role;
    public string currentWeapon = "M416";
    
    [Header("📊 Combat Stats")]
    public float health = 100f;
    public float maxHealth = 100f;
    public int kills = 0;
    public int deaths = 0;
    public int assists = 0;
    public float accuracy = 0f;
    
    [Header("🎯 Tactical Settings")]
    public float engagementRange = 30f;
    public float coverSeekDistance = 15f;
    public float teamCoordinationRange = 20f;
    public bool hasSpawnProtection = false;
    
    [Header("🏃‍♂️ Movement")]
    public MovementState currentMovementState = MovementState.Walking;
    public float movementSpeed = 6f;
    public float sprintSpeed = 9f;
    public bool isInCover = false;
    
    // Components
    private UnityEngine.AI.NavMeshAgent navAgent;
    private Rigidbody rb;
    private PUBGTDMManager gameManager;
    
    // Tactical data
    private Vector3 lastKnownEnemyPosition;
    private float lastEnemySpottedTime;
    private List<PUBGAgent> visibleEnemies = new List<PUBGAgent>();
    private List<PUBGAgent> teammates = new List<PUBGAgent>();
    private Vector3 currentCoverPosition;
    private WeaponData weaponData;
    
    // Decision making
    private float lastDecisionTime;
    private string currentTactic = "patrol";
    private Vector3 targetPosition;
    private PUBGAgent currentTarget;
    
    public void Initialize(PUBGTeam assignedTeam, PUBGRole assignedRole, PUBGTDMManager manager)
    {
        team = assignedTeam;
        role = assignedRole;
        gameManager = manager;
        
        // Get components
        navAgent = GetComponent<UnityEngine.AI.NavMeshAgent>();
        rb = GetComponent<Rigidbody>();
        
        // Configure NavMeshAgent
        if (navAgent != null)
        {
            navAgent.speed = movementSpeed;
            navAgent.acceleration = 12f;
            navAgent.angularSpeed = 360f;
        }
        
        // Set initial health
        health = maxHealth;
        
        Debug.Log($"🤖 {name} initialized as {role} for {team}");
    }
    
    public void AssignWeapon(string weaponName)
    {
        currentWeapon = weaponName;
        weaponData = gameManager?.GetWeaponData(weaponName);
        
        if (weaponData != null)
        {
            engagementRange = weaponData.range;
            Debug.Log($"🔫 {name} equipped with {weaponName} (Range: {engagementRange}m)");
        }
    }
    
    public override void OnEpisodeBegin()
    {
        // Reset agent state
        health = maxHealth;
        kills = 0;
        deaths = 0;
        assists = 0;
        
        // Reset position
        if (gameManager != null)
        {
            Vector3 spawnPos = gameManager.GetRespawnPosition(team);
            transform.position = spawnPos;
        }
        
        // Clear tactical data
        visibleEnemies.Clear();
        currentTarget = null;
        lastEnemySpottedTime = 0f;
    }
    
    public override void CollectObservations(VectorSensor sensor)
    {
        // Agent state (8 observations)
        sensor.AddObservation(health / maxHealth); // Normalized health
        sensor.AddObservation(transform.position); // Position (3)
        sensor.AddObservation(transform.forward); // Forward direction (3)
        sensor.AddObservation(hasSpawnProtection ? 1f : 0f); // Spawn protection
        
        // Movement state (6 observations)
        sensor.AddObservation((int)currentMovementState);
        sensor.AddObservation(navAgent.velocity.magnitude / sprintSpeed); // Normalized speed
        sensor.AddObservation(isInCover ? 1f : 0f);
        sensor.AddObservation(navAgent.remainingDistance);
        sensor.AddObservation(Vector3.Distance(transform.position, targetPosition));
        sensor.AddObservation(navAgent.hasPath ? 1f : 0f);
        
        // Combat state (10 observations)
        sensor.AddObservation(visibleEnemies.Count);
        sensor.AddObservation(currentTarget != null ? 1f : 0f);
        sensor.AddObservation(Time.time - lastEnemySpottedTime);
        sensor.AddObservation(lastKnownEnemyPosition);
        sensor.AddObservation(weaponData?.damage ?? 0f);
        sensor.AddObservation(weaponData?.fireRate ?? 0f);
        sensor.AddObservation(weaponData?.accuracy ?? 0f);
        
        // Team coordination (8 observations)
        sensor.AddObservation(teammates.Count);
        Vector3 avgTeammatePos = GetAverageTeammatePosition();
        sensor.AddObservation(avgTeammatePos);
        sensor.AddObservation(Vector3.Distance(transform.position, avgTeammatePos));
        sensor.AddObservation(GetNearbyTeammateCount());
        
        // Tactical environment (6 observations)
        sensor.AddObservation(GetNearestCoverDistance());
        sensor.AddObservation(currentCoverPosition);
        sensor.AddObservation(IsInChokePoint() ? 1f : 0f);
        
        // Match state (4 observations)
        if (gameManager != null)
        {
            sensor.AddObservation(gameManager.teamAKills);
            sensor.AddObservation(gameManager.teamBKills);
            sensor.AddObservation(gameManager.matchTimer / gameManager.matchDuration);
            sensor.AddObservation(gameManager.matchActive ? 1f : 0f);
        }
        else
        {
            sensor.AddObservation(0f);
            sensor.AddObservation(0f);
            sensor.AddObservation(0f);
            sensor.AddObservation(0f);
        }
    }
    
    public override void OnActionReceived(ActionBuffers actions)
    {
        if (!gameManager.matchActive || hasSpawnProtection) return;
        
        // Movement actions
        Vector3 moveDirection = Vector3.zero;
        moveDirection.x = Mathf.Clamp(actions.ContinuousActions[0], -1f, 1f);
        moveDirection.z = Mathf.Clamp(actions.ContinuousActions[1], -1f, 1f);
        
        // Discrete actions
        int movementAction = actions.DiscreteActions[0]; // 0=walk, 1=run, 2=crouch, 3=prone
        int combatAction = actions.DiscreteActions[1]; // 0=none, 1=shoot, 2=reload, 3=seek_cover
        int tacticalAction = actions.DiscreteActions[2]; // 0=patrol, 1=attack, 2=defend, 3=flank
        
        // Apply movement
        ApplyMovement(moveDirection, movementAction);
        
        // Apply combat actions
        ApplyCombatAction(combatAction);
        
        // Apply tactical actions
        ApplyTacticalAction(tacticalAction);
        
        // Update observations
        UpdateVisibleEnemies();
        UpdateTeammates();
        
        // Reward shaping
        CalculateRewards();
    }
    
    void ApplyMovement(Vector3 direction, int movementAction)
    {
        if (navAgent == null) return;
        
        // Set movement state
        switch (movementAction)
        {
            case 0: currentMovementState = MovementState.Walking; navAgent.speed = movementSpeed; break;
            case 1: currentMovementState = MovementState.Running; navAgent.speed = sprintSpeed; break;
            case 2: currentMovementState = MovementState.Crouching; navAgent.speed = movementSpeed * 0.5f; break;
            case 3: currentMovementState = MovementState.Prone; navAgent.speed = movementSpeed * 0.25f; break;
        }
        
        // Apply movement
        if (direction.magnitude > 0.1f)
        {
            Vector3 targetPos = transform.position + direction.normalized * 5f;
            navAgent.SetDestination(targetPos);
        }
    }
    
    void ApplyCombatAction(int action)
    {
        switch (action)
        {
            case 1: // Shoot
                if (currentTarget != null && CanShoot())
                {
                    Shoot(currentTarget);
                }
                break;
            case 2: // Reload
                // Reload animation/logic
                break;
            case 3: // Seek cover
                SeekNearestCover();
                break;
        }
    }
    
    void ApplyTacticalAction(int action)
    {
        switch (action)
        {
            case 0: currentTactic = "patrol"; break;
            case 1: currentTactic = "attack"; break;
            case 2: currentTactic = "defend"; break;
            case 3: currentTactic = "flank"; break;
        }
    }
    
    bool CanShoot()
    {
        return weaponData != null && !hasSpawnProtection && health > 0;
    }
    
    void Shoot(PUBGAgent target)
    {
        if (target == null || target.health <= 0) return;
        
        float distance = Vector3.Distance(transform.position, target.transform.position);
        if (distance > engagementRange) return;
        
        // Calculate hit chance based on weapon accuracy and distance
        float hitChance = weaponData.accuracy * (1f - (distance / engagementRange) * 0.5f);
        
        if (Random.value < hitChance)
        {
            // Hit! Calculate damage
            float damage = weaponData.damage;
            
            // Apply damage multipliers (simplified)
            if (Random.value < 0.1f) damage *= 2f; // Headshot chance
            
            target.TakeDamage(damage, this);
            
            // Reward for hitting
            AddReward(0.1f);
        }
        else
        {
            // Miss
            AddReward(-0.01f);
        }
    }
    
    public void TakeDamage(float damage, PUBGAgent attacker)
    {
        if (hasSpawnProtection) return;
        
        health -= damage;
        health = Mathf.Max(0, health);
        
        // Penalty for taking damage
        AddReward(-0.1f);
        
        if (health <= 0)
        {
            Die(attacker);
        }
    }
    
    void Die(PUBGAgent killer)
    {
        deaths++;
        
        // Major penalty for dying
        AddReward(-1.0f);
        
        // Reward killer
        if (killer != null && killer.team != team)
        {
            killer.RegisterKill(this);
            gameManager?.RegisterKill(killer, this);
        }
        
        // End episode
        EndEpisode();
    }
    
    public void RegisterKill(PUBGAgent victim)
    {
        kills++;
        AddReward(1.0f); // Major reward for kill
        
        Debug.Log($"💀 {name} eliminated {victim.name}! Kills: {kills}");
    }
    
    public void Respawn(Vector3 position)
    {
        transform.position = position;
        health = maxHealth;
        hasSpawnProtection = true;
        
        // Clear combat state
        currentTarget = null;
        visibleEnemies.Clear();
        
        Debug.Log($"🔄 {name} respawned at {position}");
    }
    
    public void SetSpawnProtection(bool enabled)
    {
        hasSpawnProtection = enabled;
        
        // Visual indication of spawn protection
        Renderer renderer = GetComponent<Renderer>();
        if (renderer != null)
        {
            renderer.material.color = enabled ? Color.yellow : (team == PUBGTeam.TeamA ? Color.blue : Color.red);
        }
    }
    
    void UpdateVisibleEnemies()
    {
        visibleEnemies.Clear();
        
        // Find all agents of opposite team within sight range
        PUBGAgent[] allAgents = FindObjectsOfType<PUBGAgent>();
        foreach (var agent in allAgents)
        {
            if (agent.team != team && agent.health > 0)
            {
                float distance = Vector3.Distance(transform.position, agent.transform.position);
                if (distance <= engagementRange && CanSeeTarget(agent))
                {
                    visibleEnemies.Add(agent);
                    lastEnemySpottedTime = Time.time;
                    lastKnownEnemyPosition = agent.transform.position;
                }
            }
        }
        
        // Select closest enemy as current target
        if (visibleEnemies.Count > 0)
        {
            currentTarget = GetClosestEnemy();
        }
        else
        {
            currentTarget = null;
        }
    }
    
    void UpdateTeammates()
    {
        teammates.Clear();
        
        PUBGAgent[] allAgents = FindObjectsOfType<PUBGAgent>();
        foreach (var agent in allAgents)
        {
            if (agent.team == team && agent != this && agent.health > 0)
            {
                teammates.Add(agent);
            }
        }
    }
    
    bool CanSeeTarget(PUBGAgent target)
    {
        // Simple line of sight check
        Vector3 direction = target.transform.position - transform.position;
        RaycastHit hit;
        
        if (Physics.Raycast(transform.position + Vector3.up, direction.normalized, out hit, direction.magnitude))
        {
            return hit.collider.gameObject == target.gameObject;
        }
        
        return true; // No obstacles
    }
    
    PUBGAgent GetClosestEnemy()
    {
        PUBGAgent closest = null;
        float closestDistance = float.MaxValue;
        
        foreach (var enemy in visibleEnemies)
        {
            float distance = Vector3.Distance(transform.position, enemy.transform.position);
            if (distance < closestDistance)
            {
                closest = enemy;
                closestDistance = distance;
            }
        }
        
        return closest;
    }
    
    Vector3 GetAverageTeammatePosition()
    {
        if (teammates.Count == 0) return transform.position;
        
        Vector3 sum = Vector3.zero;
        foreach (var teammate in teammates)
        {
            sum += teammate.transform.position;
        }
        
        return sum / teammates.Count;
    }
    
    int GetNearbyTeammateCount()
    {
        int count = 0;
        foreach (var teammate in teammates)
        {
            if (Vector3.Distance(transform.position, teammate.transform.position) <= teamCoordinationRange)
            {
                count++;
            }
        }
        return count;
    }
    
    float GetNearestCoverDistance()
    {
        // Simplified - would use actual cover points from config
        return 5f;
    }
    
    void SeekNearestCover()
    {
        // Simplified cover seeking
        Vector3 coverPos = transform.position + Random.insideUnitSphere * 10f;
        coverPos.y = transform.position.y;
        
        if (navAgent != null)
        {
            navAgent.SetDestination(coverPos);
        }
    }
    
    bool IsInChokePoint()
    {
        // Simplified - would check against actual choke points from config
        return false;
    }
    
    void CalculateRewards()
    {
        // Survival reward
        AddReward(0.001f);
        
        // Team coordination reward
        if (GetNearbyTeammateCount() > 0)
        {
            AddReward(0.01f);
        }
        
        // Cover usage reward
        if (isInCover)
        {
            AddReward(0.005f);
        }
    }
}
