using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

/// <summary>
/// 🎮 PUBG TDM Scene Setup - Creates authentic PUBG Mobile 5v5 TDM environment
/// Generates warehouse arena with cover points, spawn zones, and tactical elements
/// </summary>
public class PUBGTDMSetup : EditorWindow
{
    [Header("🏟️ Arena Configuration")]
    public Vector3 arenaSize = new Vector3(100, 15, 80);
    public Material arenaMaterial;
    public Material coverMaterial;
    
    [Header("📦 Cover Elements")]
    public int cratesPerLane = 5;
    public int barrelsPerLane = 3;
    public int wallsPerLane = 2;
    
    [Header("🎯 Spawn Configuration")]
    public float spawnSafeZoneRadius = 15f;
    public Vector3 teamASpawnCenter = new Vector3(-40, 0, 20);
    public Vector3 teamBSpawnCenter = new Vector3(40, 0, 20);
    
    [MenuItem("SquadMate AI/🎮 PUBG TDM Setup")]
    public static void ShowWindow()
    {
        PUBGTDMSetup window = GetWindow<PUBGTDMSetup>("PUBG TDM Setup");
        window.minSize = new Vector2(400, 600);
        window.Show();
        Debug.Log("🎮 PUBG TDM Setup window opened");
    }
    
    void OnGUI()
    {
        GUILayout.Label("🎮 PUBG Mobile 5v5 TDM Setup", EditorStyles.boldLabel);
        GUILayout.Space(10);
        
        EditorGUILayout.HelpBox("Creates an authentic PUBG Mobile warehouse arena with:\n\n" +
                               "• Symmetric 3-lane layout\n" +
                               "• Realistic cover elements\n" +
                               "• Tactical spawn zones\n" +
                               "• Elevated positions\n" +
                               "• Choke points and sight lines", MessageType.Info);
        
        GUILayout.Space(10);
        
        // Arena Configuration
        GUILayout.Label("🏟️ Arena Configuration", EditorStyles.boldLabel);
        arenaSize = EditorGUILayout.Vector3Field("Arena Size", arenaSize);
        arenaMaterial = EditorGUILayout.ObjectField("Arena Material", arenaMaterial, typeof(Material), false) as Material;
        coverMaterial = EditorGUILayout.ObjectField("Cover Material", coverMaterial, typeof(Material), false) as Material;
        
        GUILayout.Space(10);
        
        // Cover Configuration
        GUILayout.Label("📦 Cover Elements", EditorStyles.boldLabel);
        cratesPerLane = EditorGUILayout.IntSlider("Crates per Lane", cratesPerLane, 1, 10);
        barrelsPerLane = EditorGUILayout.IntSlider("Barrels per Lane", barrelsPerLane, 1, 8);
        wallsPerLane = EditorGUILayout.IntSlider("Walls per Lane", wallsPerLane, 1, 5);
        
        GUILayout.Space(10);
        
        // Spawn Configuration
        GUILayout.Label("🎯 Spawn Configuration", EditorStyles.boldLabel);
        spawnSafeZoneRadius = EditorGUILayout.FloatField("Safe Zone Radius", spawnSafeZoneRadius);
        teamASpawnCenter = EditorGUILayout.Vector3Field("Team A Spawn Center", teamASpawnCenter);
        teamBSpawnCenter = EditorGUILayout.Vector3Field("Team B Spawn Center", teamBSpawnCenter);
        
        GUILayout.Space(20);
        
        // Setup Buttons
        if (GUILayout.Button("🏗️ Create Arena Floor", GUILayout.Height(30)))
        {
            CreateArenaFloor();
        }
        
        if (GUILayout.Button("📦 Generate Cover Elements", GUILayout.Height(30)))
        {
            GenerateCoverElements();
        }
        
        if (GUILayout.Button("🎯 Setup Spawn Points", GUILayout.Height(30)))
        {
            SetupSpawnPoints();
        }
        
        if (GUILayout.Button("🎮 Complete PUBG TDM Arena", GUILayout.Height(40)))
        {
            CreateCompletePUBGArena();
        }
        
        GUILayout.Space(10);
        
        if (GUILayout.Button("🧹 Clear Arena", GUILayout.Height(30)))
        {
            ClearArena();
        }
    }
    
    void CreateArenaFloor()
    {
        Debug.Log("🏗️ Creating PUBG TDM arena floor...");
        
        // Create main floor
        GameObject floor = GameObject.CreatePrimitive(PrimitiveType.Plane);
        floor.name = "PUBG_Arena_Floor";
        floor.transform.localScale = new Vector3(arenaSize.x / 10f, 1, arenaSize.z / 10f);
        floor.transform.position = Vector3.zero;
        
        // Apply material
        if (arenaMaterial != null)
        {
            floor.GetComponent<Renderer>().material = arenaMaterial;
        }
        
        // Create walls
        CreateArenaWalls();
        
        Debug.Log("✅ Arena floor created");
    }
    
    void CreateArenaWalls()
    {
        GameObject wallParent = new GameObject("Arena_Walls");
        
        // Create 4 walls
        CreateWall("North_Wall", new Vector3(0, arenaSize.y / 2, arenaSize.z / 2), new Vector3(arenaSize.x, arenaSize.y, 1), wallParent.transform);
        CreateWall("South_Wall", new Vector3(0, arenaSize.y / 2, -arenaSize.z / 2), new Vector3(arenaSize.x, arenaSize.y, 1), wallParent.transform);
        CreateWall("East_Wall", new Vector3(arenaSize.x / 2, arenaSize.y / 2, 0), new Vector3(1, arenaSize.y, arenaSize.z), wallParent.transform);
        CreateWall("West_Wall", new Vector3(-arenaSize.x / 2, arenaSize.y / 2, 0), new Vector3(1, arenaSize.y, arenaSize.z), wallParent.transform);
    }
    
    void CreateWall(string name, Vector3 position, Vector3 scale, Transform parent)
    {
        GameObject wall = GameObject.CreatePrimitive(PrimitiveType.Cube);
        wall.name = name;
        wall.transform.position = position;
        wall.transform.localScale = scale;
        wall.transform.SetParent(parent);
        
        if (arenaMaterial != null)
        {
            wall.GetComponent<Renderer>().material = arenaMaterial;
        }
    }
    
    void GenerateCoverElements()
    {
        Debug.Log("📦 Generating PUBG TDM cover elements...");
        
        GameObject coverParent = new GameObject("Cover_Elements");
        
        // Generate cover for each lane
        GenerateLaneCover("Left_Lane", new Vector3(-25, 0, 0), coverParent.transform);
        GenerateLaneCover("Center_Lane", new Vector3(0, 0, 0), coverParent.transform);
        GenerateLaneCover("Right_Lane", new Vector3(25, 0, 0), coverParent.transform);
        
        // Generate elevated positions
        GenerateElevatedPositions(coverParent.transform);
        
        Debug.Log("✅ Cover elements generated");
    }
    
    void GenerateLaneCover(string laneName, Vector3 laneCenter, Transform parent)
    {
        GameObject laneParent = new GameObject(laneName);
        laneParent.transform.SetParent(parent);
        
        // Generate crates
        for (int i = 0; i < cratesPerLane; i++)
        {
            Vector3 cratePos = laneCenter + new Vector3(
                Random.Range(-8f, 8f),
                0.75f,
                Random.Range(-30f, 30f)
            );
            CreateCoverElement("Crate", cratePos, new Vector3(1.5f, 1.5f, 1.5f), laneParent.transform);
        }
        
        // Generate barrels
        for (int i = 0; i < barrelsPerLane; i++)
        {
            Vector3 barrelPos = laneCenter + new Vector3(
                Random.Range(-6f, 6f),
                0.5f,
                Random.Range(-25f, 25f)
            );
            CreateCoverElement("Barrel", barrelPos, new Vector3(1f, 1f, 1f), laneParent.transform);
        }
        
        // Generate walls
        for (int i = 0; i < wallsPerLane; i++)
        {
            Vector3 wallPos = laneCenter + new Vector3(
                Random.Range(-10f, 10f),
                1f,
                Random.Range(-20f, 20f)
            );
            CreateCoverElement("Wall", wallPos, new Vector3(3f, 2f, 0.5f), laneParent.transform);
        }
    }
    
    void CreateCoverElement(string type, Vector3 position, Vector3 scale, Transform parent)
    {
        GameObject cover = GameObject.CreatePrimitive(PrimitiveType.Cube);
        cover.name = $"Cover_{type}_{Random.Range(1000, 9999)}";
        cover.transform.position = position;
        cover.transform.localScale = scale;
        cover.transform.SetParent(parent);
        
        // Add cover tag
        cover.tag = "Cover";
        
        // Apply material
        if (coverMaterial != null)
        {
            cover.GetComponent<Renderer>().material = coverMaterial;
        }
        
        // Add random rotation for variety
        cover.transform.rotation = Quaternion.Euler(0, Random.Range(0, 360), 0);
    }
    
    void GenerateElevatedPositions(Transform parent)
    {
        GameObject elevatedParent = new GameObject("Elevated_Positions");
        elevatedParent.transform.SetParent(parent);
        
        // Left elevated position
        CreateElevatedPosition("Left_Ramp", new Vector3(-35, 0, 15), elevatedParent.transform);
        
        // Right elevated position
        CreateElevatedPosition("Right_Ramp", new Vector3(35, 0, 15), elevatedParent.transform);
        
        // Center elevated position
        CreateElevatedPosition("Center_Platform", new Vector3(0, 0, 25), elevatedParent.transform);
    }
    
    void CreateElevatedPosition(string name, Vector3 position, Transform parent)
    {
        // Create ramp
        GameObject ramp = GameObject.CreatePrimitive(PrimitiveType.Cube);
        ramp.name = name + "_Ramp";
        ramp.transform.position = position;
        ramp.transform.localScale = new Vector3(8f, 0.5f, 6f);
        ramp.transform.SetParent(parent);
        
        // Create platform
        GameObject platform = GameObject.CreatePrimitive(PrimitiveType.Cube);
        platform.name = name + "_Platform";
        platform.transform.position = position + new Vector3(0, 2f, 0);
        platform.transform.localScale = new Vector3(6f, 0.2f, 4f);
        platform.transform.SetParent(parent);
        
        if (arenaMaterial != null)
        {
            ramp.GetComponent<Renderer>().material = arenaMaterial;
            platform.GetComponent<Renderer>().material = arenaMaterial;
        }
    }
    
    void SetupSpawnPoints()
    {
        Debug.Log("🎯 Setting up PUBG TDM spawn points...");
        
        GameObject spawnParent = new GameObject("PUBG_Spawn_Points");
        
        // Create Team A spawns
        CreateTeamSpawns("TeamA", teamASpawnCenter, spawnParent.transform);
        
        // Create Team B spawns
        CreateTeamSpawns("TeamB", teamBSpawnCenter, spawnParent.transform);
        
        Debug.Log("✅ Spawn points created");
    }
    
    void CreateTeamSpawns(string teamName, Vector3 center, Transform parent)
    {
        GameObject teamParent = new GameObject($"{teamName}_Spawns");
        teamParent.transform.SetParent(parent);
        
        // Create 5 spawn points in a formation
        Vector3[] spawnOffsets = {
            new Vector3(0, 0, 5),      // Center
            new Vector3(-3, 0, 2),     // Left front
            new Vector3(3, 0, 2),      // Right front
            new Vector3(-5, 0, -2),    // Left back
            new Vector3(5, 0, -2)      // Right back
        };
        
        for (int i = 0; i < spawnOffsets.Length; i++)
        {
            GameObject spawn = new GameObject($"{teamName}_Spawn_{i + 1}");
            spawn.transform.position = center + spawnOffsets[i];
            spawn.transform.SetParent(teamParent.transform);
            spawn.tag = $"{teamName}Spawn";
            
            // Add visual indicator
            GameObject indicator = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
            indicator.name = "Spawn_Indicator";
            indicator.transform.position = spawn.transform.position + Vector3.up * 0.1f;
            indicator.transform.localScale = new Vector3(1f, 0.1f, 1f);
            indicator.transform.SetParent(spawn.transform);
            
            Renderer renderer = indicator.GetComponent<Renderer>();
            renderer.material = new Material(Shader.Find("Standard"));
            renderer.material.color = teamName == "TeamA" ? Color.blue : Color.red;
        }
        
        // Create safe zone indicator
        CreateSafeZoneIndicator(teamName, center, teamParent.transform);
    }
    
    void CreateSafeZoneIndicator(string teamName, Vector3 center, Transform parent)
    {
        GameObject safeZone = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        safeZone.name = $"{teamName}_SafeZone";
        safeZone.transform.position = center + Vector3.up * 0.05f;
        safeZone.transform.localScale = new Vector3(spawnSafeZoneRadius * 2, 0.05f, spawnSafeZoneRadius * 2);
        safeZone.transform.SetParent(parent);
        
        Renderer renderer = safeZone.GetComponent<Renderer>();
        renderer.material = new Material(Shader.Find("Standard"));
        Color safeZoneColor = teamName == "TeamA" ? Color.blue : Color.red;
        safeZoneColor.a = 0.3f;
        renderer.material.color = safeZoneColor;
        
        // Remove collider
        DestroyImmediate(safeZone.GetComponent<Collider>());
    }
    
    void CreateCompletePUBGArena()
    {
        Debug.Log("🎮 Creating complete PUBG TDM arena...");
        
        CreateArenaFloor();
        GenerateCoverElements();
        SetupSpawnPoints();
        
        // Add game manager
        GameObject gameManager = new GameObject("PUBG_TDM_Manager");
        PUBGTDMManager manager = gameManager.AddComponent<PUBGTDMManager>();
        
        // Load config file
        TextAsset config = AssetDatabase.LoadAssetAtPath<TextAsset>("Assets/Data/PUBG_TDM_Config.json");
        if (config != null)
        {
            manager.configFile = config;
        }
        
        Debug.Log("✅ Complete PUBG TDM arena created!");
        EditorUtility.DisplayDialog("PUBG TDM Setup Complete", 
            "PUBG Mobile 5v5 TDM arena created successfully!\n\n" +
            "Features:\n" +
            "• Authentic warehouse layout\n" +
            "• 3-lane tactical design\n" +
            "• Realistic cover elements\n" +
            "• Team spawn zones\n" +
            "• Elevated positions\n\n" +
            "Ready for AI training!", "OK");
    }
    
    void ClearArena()
    {
        Debug.Log("🧹 Clearing PUBG TDM arena...");
        
        string[] objectsToDelete = {
            "PUBG_Arena_Floor",
            "Arena_Walls", 
            "Cover_Elements",
            "PUBG_Spawn_Points",
            "Elevated_Positions",
            "PUBG_TDM_Manager"
        };
        
        foreach (string objName in objectsToDelete)
        {
            GameObject obj = GameObject.Find(objName);
            if (obj != null)
            {
                DestroyImmediate(obj);
            }
        }
        
        Debug.Log("✅ Arena cleared");
    }
}
