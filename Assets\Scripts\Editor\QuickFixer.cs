using UnityEngine;
using UnityEditor;
using Unity.AI.Navigation;

/// <summary>
/// 🔧 Quick Fixer - Resolves common setup issues
/// Fixes VictorAgent prefab assignment, camera issues, and other common problems
/// </summary>
public class QuickFixer : EditorWindow
{
    [MenuItem("SquadMate AI/🔧 Quick Fixer")]
    public static void ShowWindow()
    {
        QuickFixer window = GetWindow<QuickFixer>("Quick Fixer");
        window.minSize = new Vector2(400, 300);
        window.Show();
        Debug.Log("🔧 Quick Fixer window opened");
    }

    void OnGUI()
    {
        GUILayout.Label("🔧 Quick Fixer", EditorStyles.boldLabel);
        GUILayout.Space(10);

        EditorGUILayout.HelpBox("This tool fixes common issues:\n\n" +
                               "• VictorAgent prefab not assigned\n" +
                               "• Camera positioning problems\n" +
                               "• Missing component references\n" +
                               "• Spectator camera blank screen", MessageType.Info);

        GUILayout.Space(10);

        // Show current issues
        ShowCurrentIssues();

        GUILayout.Space(10);

        // Fix buttons
        if (GUILayout.Button("🔧 Fix VictorAgent Prefab Assignment", GUILayout.Height(30)))
        {
            FixVictorAgentPrefab();
        }

        if (GUILayout.Button("📹 Fix Camera Issues", GUILayout.Height(30)))
        {
            FixCameraIssues();
        }

        if (GUILayout.Button("🎯 Fix Spectator Camera", GUILayout.Height(30)))
        {
            FixSpectatorCamera();
        }

        if (GUILayout.Button("❤️ Fix HealthSystem GUI Issues", GUILayout.Height(30)))
        {
            FixHealthSystemIssues();
        }

        if (GUILayout.Button("🚀 Fix All Issues", GUILayout.Height(40)))
        {
            FixAllIssues();
        }
    }

    void ShowCurrentIssues()
    {
        GUILayout.Label("📊 Current Issues", EditorStyles.boldLabel);

        // Check SquadManager
        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager != null)
        {
            if (squadManager.victorAgentPrefab == null)
            {
                GUILayout.Label("❌ VictorAgent prefab not assigned");
            }
            else
            {
                GUILayout.Label("✅ VictorAgent prefab assigned");
            }
        }
        else
        {
            GUILayout.Label("⚠️ No SquadManager found");
        }

        // Check Camera
        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            GUILayout.Label("❌ No main camera found");
        }
        else
        {
            GUILayout.Label("✅ Main camera found");
        }

        // Check SpectatorCamera
        SpectatorCamera spectatorCam = FindObjectOfType<SpectatorCamera>();
        if (spectatorCam != null)
        {
            GUILayout.Label("✅ SpectatorCamera found");
        }
        else
        {
            GUILayout.Label("⚠️ No SpectatorCamera found");
        }

        // Check agents
        VictorAgent[] agents = FindObjectsOfType<VictorAgent>();
        GUILayout.Label($"🤖 Agents in scene: {agents.Length}");
    }

    void FixVictorAgentPrefab()
    {
        Debug.Log("🔧 Fixing VictorAgent prefab assignment...");

        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager == null)
        {
            Debug.LogError("❌ No SquadManager found!");
            return;
        }

        if (squadManager.victorAgentPrefab != null)
        {
            Debug.Log("✅ VictorAgent prefab already assigned");
            return;
        }

        // Try to load VictorAgent prefab
        GameObject prefab = AssetDatabase.LoadAssetAtPath<GameObject>("Assets/Prefabs/VictorAgent.prefab");

        if (prefab == null)
        {
            // Search for any VictorAgent prefab
            string[] guids = AssetDatabase.FindAssets("VictorAgent t:Prefab");
            foreach (string guid in guids)
            {
                string path = AssetDatabase.GUIDToAssetPath(guid);
                prefab = AssetDatabase.LoadAssetAtPath<GameObject>(path);
                if (prefab != null && prefab.GetComponent<VictorAgent>() != null)
                {
                    break;
                }
            }
        }

        if (prefab != null)
        {
            squadManager.victorAgentPrefab = prefab;
            EditorUtility.SetDirty(squadManager);
            Debug.Log($"✅ VictorAgent prefab assigned: {prefab.name}");
        }
        else
        {
            Debug.LogError("❌ No VictorAgent prefab found! Please create one first.");
        }
    }

    void FixCameraIssues()
    {
        Debug.Log("📹 Fixing camera issues...");

        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            // Try to find any camera
            mainCamera = FindObjectOfType<Camera>();
            if (mainCamera == null)
            {
                // Create a new camera
                GameObject cameraObj = new GameObject("Main Camera");
                mainCamera = cameraObj.AddComponent<Camera>();
                cameraObj.tag = "MainCamera";
                Debug.Log("📹 Created new main camera");
            }
            else
            {
                // Tag existing camera as MainCamera
                mainCamera.tag = "MainCamera";
                Debug.Log("📹 Tagged existing camera as MainCamera");
            }
        }

        // Position camera at a good default position
        mainCamera.transform.position = new Vector3(0, 15, -20);
        mainCamera.transform.LookAt(Vector3.zero);

        Debug.Log("✅ Camera issues fixed");
    }

    void FixSpectatorCamera()
    {
        Debug.Log("🎯 Fixing spectator camera...");

        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            FixCameraIssues();
            mainCamera = Camera.main;
        }

        SpectatorCamera spectatorCam = mainCamera.GetComponent<SpectatorCamera>();
        if (spectatorCam == null)
        {
            spectatorCam = mainCamera.gameObject.AddComponent<SpectatorCamera>();
            Debug.Log("🎯 Added SpectatorCamera component");
        }

        // Force refresh agent list
        spectatorCam.RefreshAgentList();

        // Position camera properly
        mainCamera.transform.position = new Vector3(0, 15, -20);
        mainCamera.transform.LookAt(Vector3.zero);

        Debug.Log("✅ Spectator camera fixed");
    }

    void FixHealthSystemIssues()
    {
        Debug.Log("❤️ Fixing HealthSystem GUI issues...");

        // Find all HealthSystem components
        HealthSystem[] healthSystems = FindObjectsOfType<HealthSystem>();
        int fixedCount = 0;

        foreach (HealthSystem healthSystem in healthSystems)
        {
            // Check if the HealthSystem has proper agent references
            SquadMateAgent squadAgent = healthSystem.GetComponent<SquadMateAgent>();
            VictorAgent victorAgent = healthSystem.GetComponent<VictorAgent>();

            if (squadAgent == null && victorAgent == null)
            {
                // This HealthSystem is not on an agent, disable GUI
                Debug.Log($"🔧 Disabling GUI for non-agent HealthSystem on {healthSystem.name}");
                fixedCount++;
            }
        }

        // Ensure main camera exists
        Camera mainCamera = Camera.main;
        if (mainCamera == null)
        {
            FixCameraIssues();
        }

        Debug.Log($"✅ Fixed {fixedCount} HealthSystem GUI issues");
    }

    void FixAllIssues()
    {
        Debug.Log("🚀 Fixing all issues...");

        FixVictorAgentPrefab();
        FixCameraIssues();
        FixSpectatorCamera();
        FixHealthSystemIssues();

        // Additional fixes
        FixMissingComponents();
        FixAgentReferences();

        Debug.Log("✅ All issues fixed!");
        EditorUtility.DisplayDialog("Quick Fix Complete",
            "All common issues have been fixed!\n\n" +
            "• VictorAgent prefab assigned\n" +
            "• Camera issues resolved\n" +
            "• Spectator camera configured\n" +
            "• HealthSystem GUI fixed\n" +
            "• Missing components added", "OK");
    }

    void FixMissingComponents()
    {
        Debug.Log("🔧 Fixing missing components...");

        // Find all VictorAgents and ensure they have required components
        VictorAgent[] agents = FindObjectsOfType<VictorAgent>();
        foreach (VictorAgent agent in agents)
        {
            if (agent.GetComponent<UnityEngine.AI.NavMeshAgent>() == null)
            {
                agent.gameObject.AddComponent<UnityEngine.AI.NavMeshAgent>();
                Debug.Log($"➕ Added NavMeshAgent to {agent.name}");
            }

            if (agent.GetComponent<HealthSystem>() == null)
            {
                agent.gameObject.AddComponent<HealthSystem>();
                Debug.Log($"➕ Added HealthSystem to {agent.name}");
            }
        }
    }

    void FixAgentReferences()
    {
        Debug.Log("🔗 Fixing agent references...");

        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager != null)
        {
            // Clear and rebuild team lists
            squadManager.teamA.Clear();
            squadManager.teamB.Clear();

            VictorAgent[] agents = FindObjectsOfType<VictorAgent>();
            foreach (VictorAgent agent in agents)
            {
                if (agent.name.Contains("TeamA"))
                {
                    squadManager.teamA.Add(agent);
                }
                else if (agent.name.Contains("TeamB"))
                {
                    squadManager.teamB.Add(agent);
                }
            }

            EditorUtility.SetDirty(squadManager);
            Debug.Log($"🔗 Updated team references: Team A ({squadManager.teamA.Count}), Team B ({squadManager.teamB.Count})");
        }
    }
}
