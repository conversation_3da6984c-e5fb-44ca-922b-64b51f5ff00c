# 🚀 Enhanced 5v5 TDM SquadMate AI

## ✨ **What's New in This Enhanced Version**

### 🔁 **1. Auto-Export Match Stats to CSV/JSON**

- **MatchLogger.cs**: Automatically exports detailed match statistics
- **Features**:
  - CSV and JSON export formats
  - Individual agent performance tracking
  - Match duration, kills, deaths, KDR, weapon usage
  - Automatic export on match end
  - Custom file paths supported

### 🎓 **2. Tournament Bracket + ELO Tracker**

- **TournamentManager.cs**: Competitive ranking system
- **Features**:
  - ELO rating system (starts at 1000)
  - Win/Loss/Draw tracking
  - Rank tiers (Bronze → Grandmaster)
  - Match history logging
  - Automatic tournament progression

### 💡 **3. Hover Tooltip Showing Role + Gear**

- **SimpleAgentTooltip.cs**: Interactive agent information (no UI dependencies)
- **Features**:
  - Hover over agents to see detailed stats
  - Real-time health, weapon, role display
  - Team color-coded tooltips
  - Combat status indicators
  - Works with built-in Unity components only

### 👁️ **4. Spectator Camera System**

- **SpectatorCamera.cs**: Multiple viewing modes for matches
- **Camera Modes**:
  - **F1**: Free Camera (WASD + mouse look)
  - **F2**: Overview (bird's eye view)
  - **F3**: Follow Agent (tracks individual agents)
  - **F4**: Combat Focus (auto-follows combat)
  - **F5**: Cinematic (smooth camera movements)

### 🎮 **5. Spectator UI**

- **SpectatorUI.cs**: Complete match information overlay (using OnGUI)
- **Features**:
  - Live match scores and timer
  - Tournament leaderboard
  - Camera controls guide
  - Toggle with F12 key
  - No external UI package dependencies

---

## 🛠️ **Setup Instructions**

### **Quick Setup (Recommended)**

1. Open Unity with your SquadMate project
2. Go to **SquadMate AI → Enhanced 5v5 Setup**
3. Assign your TDM arena prefab (from `Assets/tdm/source/Tdm.fbx`)
4. Assign Victor agent prefab
5. Click **"Complete 5v5 Setup"**
6. Press **Play** to start the enhanced 5v5 match!

### **Manual Setup**

1. **Fix Arena Position**: Use "Fix TDM Arena Position Only" if buildings are misplaced
2. **Add Components**: Manually add MatchLogger, TournamentManager to GameManager
3. **Setup Camera**: Add SpectatorCamera to your main camera
4. **Add UI**: Add SpectatorUI component to scene

---

## 🎮 **Controls & Usage**

### **Spectator Controls**

| Key | Action |
|-----|--------|
| **F1** | Free Camera Mode |
| **F2** | Overview Mode |
| **F3** | Follow Agent Mode |
| **F4** | Combat Focus Mode |
| **F5** | Cinematic Mode |
| **Tab** | Next Agent |
| **Shift** | Previous Agent |
| **Mouse Wheel** | Zoom In/Out |
| **F12** | Toggle UI |

### **Free Camera Controls**

- **WASD**: Move horizontally
- **Q/E**: Move up/down
- **Right Click + Mouse**: Look around

### **Agent Interaction**

- **Hover** over any agent to see detailed tooltip
- **Tooltip shows**: Role, team, health, weapon, combat stats
- **Color-coded** by team (Blue = Team A, Red = Team B)

---

## 📊 **Data Export & Analysis**

### **Match Statistics Export**

- **Location**: `Application.persistentDataPath` (usually `%USERPROFILE%/AppData/LocalLow/[CompanyName]/[ProjectName]/`)
- **Files**:
  - `match_results.csv`: Spreadsheet format for analysis
  - `match_results.json`: Structured data for programming
- **Data Includes**: Agent name, team, role, kills, deaths, KDR, survival time, weapons used

### **Tournament Tracking**

- **ELO System**: Dynamic rating based on match performance
- **Ranks**: Bronze (800) → Silver (1000) → Gold (1200) → Platinum (1400) → Diamond (1600) → Master (1800) → Grandmaster (2000+)
- **Statistics**: Win rate, total matches, average KDR

---

## 🏆 **Tournament System**

### **How ELO Works**

- **Starting ELO**: 1000 points
- **Win**: Gain points based on opponent's rating
- **Loss**: Lose points based on opponent's rating
- **Draw**: Small adjustment toward average

### **Viewing Rankings**

- **In-Game**: Check SpectatorUI leaderboard (top-right)
- **Console**: Tournament standings printed after each match
- **Debug**: Use context menu "Print Leaderboard" on TournamentManager

---

## 🔧 **Customization Options**

### **MatchLogger Settings**

```csharp
public bool exportToCSV = true;
public bool exportToJSON = true;
public bool autoExportOnMatchEnd = true;
public string customOutputPath = ""; // Custom export location
```

### **Tournament Settings**

```csharp
public float eloKFactor = 32f; // ELO change rate
public float initialELO = 1000f; // Starting rating
public bool autoStartNextMatch = true;
```

### **Spectator Camera Settings**

```csharp
public float moveSpeed = 10f;
public float followHeight = 8f;
public bool autoSwitchTargets = true;
public float autoSwitchInterval = 10f;
```

---

## 🎯 **5v5 Match Features**

### **Team Setup**

- **Team A**: 5 AI agents (Blue team)
- **Team B**: 5 AI agents (Red team)
- **Spawn Points**: Strategically placed on opposite sides
- **Roles**: Support, Assault, Scout, Anchor (automatically assigned)

### **Match Flow**

1. **Pre-Match**: Agents spawn and initialize
2. **Active Match**: 5v5 combat with objectives
3. **Match End**: Score limit or time limit reached
4. **Post-Match**: Statistics exported, ELO updated
5. **Next Match**: Automatic restart with fresh spawns

### **Win Conditions**

- **Score Limit**: First team to reach target score
- **Time Limit**: Highest score when time expires
- **Elimination**: All enemy agents eliminated

---

## 📦 **Unity Package Creation**

Use the **"Create Unity Package"** button in Enhanced 5v5 Setup to export:

- All scripts and systems
- Prefabs and materials
- Training scene
- Ready-to-import package for other projects

---

## 🐛 **Troubleshooting**

### **Arena Position Issues**

- Use **"Fix TDM Arena Position Only"** button
- Manually set arena position to (0,0,0)
- Adjust scale to (0.1, 0.1, 0.1) if too large

### **Missing Components**

- Run **Enhanced 5v5 Setup** to auto-add missing systems
- Check console for component status
- Ensure all prefabs are properly assigned

### **Performance Issues**

- Reduce team size if needed
- Disable detailed tooltips for better performance
- Use Overview camera mode for less processing

---

## 🎉 **Enjoy Your Enhanced 5v5 TDM Experience!**

Your AI agents now compete in a fully-featured tournament system with comprehensive statistics tracking and professional spectator tools. Watch them evolve, analyze their performance, and enjoy the enhanced tactical gameplay!

**Happy Training! 🤖⚔️**
