# 🔧 Enhanced 5v5 Setup Menu Troubleshooting

## ✅ **Issue Fixed!**

The compilation error in `Enhanced5v5Setup.cs` has been resolved:
- **Fixed**: `mainCam.AddComponent<SpectatorCamera>()` → `mainCam.gameObject.AddComponent<SpectatorCamera>()`

---

## 🔍 **If Menu Still Not Visible:**

### **1. Check Compilation Status**
- Open **Console** window (`Window → General → Console`)
- Look for any **red error messages**
- All scripts must compile successfully for menu items to appear

### **2. Verify Script Location**
- Ensure `Enhanced5v5Setup.cs` is in `Assets/Scripts/Editor/` folder
- Editor scripts **must** be in an `Editor` folder to work

### **3. Force Recompilation**
```
1. Right-click on Assets/Scripts folder
2. Select "Reimport"
3. Wait for compilation to complete
4. Check if menu appears
```

### **4. Restart Unity Editor**
```
1. Save your scene
2. Close Unity completely
3. Reopen Unity
4. Check SquadMate AI menu
```

### **5. Manual Menu Access**
If the menu still doesn't appear, you can access it manually:

```csharp
// In Unity Console window, type:
Enhanced5v5Setup.ShowWindow();
```

---

## 🎯 **Alternative Access Methods:**

### **Method 1: Test Menu**
1. Go to **SquadMate AI → Test Menu**
2. If this works, the menu system is functional
3. Try **SquadMate AI → Open Enhanced 5v5 Setup**

### **Method 2: Window Menu**
1. Go to **Window → General → Enhanced 5v5 Setup** (if available)
2. This bypasses the custom menu structure

### **Method 3: Script Execution**
1. Create a new script with this content:
```csharp
using UnityEngine;
using UnityEditor;

public class OpenSetup
{
    [MenuItem("Tools/Open Enhanced Setup")]
    public static void Open()
    {
        Enhanced5v5Setup.ShowWindow();
    }
}
```

---

## 🔧 **Manual Setup (If Menu Fails)**

If the menu continues to be problematic, you can set up the enhanced 5v5 system manually:

### **Step 1: Fix Arena Position**
```csharp
// Find TDM arena
GameObject arena = GameObject.Find("Tdm");
if (arena != null) {
    arena.transform.position = Vector3.zero;
    arena.transform.localScale = new Vector3(0.1f, 0.1f, 0.1f);
}
```

### **Step 2: Add Enhanced Systems**
```csharp
// Add MatchLogger
GameObject gameManager = new GameObject("GameManager");
gameManager.AddComponent<MatchLogger>();
gameManager.AddComponent<TournamentManager>();
gameManager.AddComponent<SquadManager>();
```

### **Step 3: Setup Spectator Camera**
```csharp
// Add to main camera
Camera.main.gameObject.AddComponent<SpectatorCamera>();
Camera.main.gameObject.AddComponent<SpectatorUI>();
```

### **Step 4: Add Tooltips to Agents**
```csharp
// Add to all VictorAgents
VictorAgent[] agents = FindObjectsOfType<VictorAgent>();
foreach(var agent in agents) {
    agent.gameObject.AddComponent<SimpleAgentTooltip>();
}
```

---

## 📊 **Verification Steps**

### **Check Required Components:**
1. **MatchLogger** - Auto-exports match statistics
2. **TournamentManager** - ELO and tournament tracking  
3. **SpectatorCamera** - Multiple camera modes
4. **SpectatorUI** - Live match information
5. **SimpleAgentTooltip** - Agent hover information
6. **SquadManager** - Enhanced 5v5 management

### **Test Functionality:**
1. **Press Play** to start the scene
2. **F1-F5** to test camera modes
3. **F12** to toggle spectator UI
4. **Hover** over agents for tooltips
5. **Check Console** for match statistics

---

## 🎮 **Controls Reference**

Once the system is working:

| Key | Action |
|-----|--------|
| **F1** | Free Camera Mode |
| **F2** | Overview Mode |
| **F3** | Follow Agent Mode |
| **F4** | Combat Focus Mode |
| **F5** | Cinematic Mode |
| **Tab** | Next Agent |
| **Shift** | Previous Agent |
| **F12** | Toggle UI |
| **Mouse Wheel** | Zoom |

---

## 🚀 **Success Indicators**

You'll know the enhanced 5v5 system is working when you see:

✅ **Console Messages:**
- "🚀 Enhanced systems initialized"
- "📊 MatchLogger added"
- "🏆 TournamentManager added"
- "👁️ SpectatorCamera added"

✅ **In-Game Features:**
- Live match scores in top-left
- Tournament standings in top-right
- Camera controls working (F1-F5)
- Agent tooltips on hover
- Match statistics in console

✅ **File Exports:**
- `match_results.csv` in persistent data path
- `match_results.json` in persistent data path
- Tournament standings logged

---

## 📞 **Still Having Issues?**

If you continue to have problems:

1. **Check Unity Version**: Ensure you're using Unity 6000.1.6f1
2. **Check ML-Agents**: Verify ML-Agents package is installed
3. **Check Console**: Look for any remaining compilation errors
4. **Try Clean Import**: Delete Library folder and reimport project

The enhanced 5v5 system should now be fully functional with all features working! 🎉
