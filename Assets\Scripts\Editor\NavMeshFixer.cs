using UnityEngine;
using UnityEditor;
using Unity.AI.Navigation;
using System.Collections.Generic;

/// <summary>
/// 🗺️ NavMesh Fixer - Resolves invalid vertex data issues
/// Filters problematic meshes and provides robust NavMesh building
/// </summary>
public class NavMeshFixer : EditorWindow
{
    private List<GameObject> filteredMeshes = new List<GameObject>();
    private bool showAdvancedOptions = false;
    private float voxelSize = 0.2f;
    private int tileSize = 256;
    private bool useCustomLayerMask = false;
    private LayerMask customLayerMask = -1;

    [MenuItem("SquadMate AI/🗺️ NavMesh Fixer")]
    public static void ShowWindow()
    {
        NavMeshFixer window = GetWindow<NavMeshFixer>("NavMesh Fixer");
        window.minSize = new Vector2(400, 500);
        window.Show();
        Debug.Log("🗺️ NavMesh Fixer window opened");
    }

    void OnGUI()
    {
        GUILayout.Label("🗺️ NavMesh Fixer", EditorStyles.boldLabel);
        GUILayout.Space(10);

        EditorGUILayout.HelpBox("This tool fixes NavMesh building issues caused by invalid vertex data in imported meshes.\n\n" +
                               "It will:\n" +
                               "• Filter out problematic meshes\n" +
                               "• Use optimized NavMesh settings\n" +
                               "• Provide fallback options\n" +
                               "• Re-enable meshes after building", MessageType.Info);

        GUILayout.Space(10);

        // Current NavMesh status
        ShowNavMeshStatus();

        GUILayout.Space(10);

        // Advanced options
        showAdvancedOptions = EditorGUILayout.Foldout(showAdvancedOptions, "⚙️ Advanced Options");
        if (showAdvancedOptions)
        {
            EditorGUI.indentLevel++;
            voxelSize = EditorGUILayout.Slider("Voxel Size", voxelSize, 0.1f, 1.0f);
            tileSize = EditorGUILayout.IntSlider("Tile Size", tileSize, 64, 512);
            useCustomLayerMask = EditorGUILayout.Toggle("Use Custom Layer Mask", useCustomLayerMask);
            if (useCustomLayerMask)
            {
                customLayerMask = EditorGUILayout.MaskField("Layer Mask", customLayerMask, UnityEditorInternal.InternalEditorUtility.layers);
            }
            EditorGUI.indentLevel--;
        }

        GUILayout.Space(10);

        // Action buttons
        if (GUILayout.Button("🔍 Analyze Scene Meshes", GUILayout.Height(30)))
        {
            AnalyzeSceneMeshes();
        }

        if (GUILayout.Button("🗺️ Fix NavMesh Issues", GUILayout.Height(40)))
        {
            FixNavMeshIssues();
        }

        if (GUILayout.Button("🧹 Clear NavMesh", GUILayout.Height(30)))
        {
            ClearNavMesh();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("📋 Export Mesh Report", GUILayout.Height(30)))
        {
            ExportMeshReport();
        }
    }

    void ShowNavMeshStatus()
    {
        GUILayout.Label("📊 Current NavMesh Status", EditorStyles.boldLabel);

        NavMeshSurface surface = FindObjectOfType<NavMeshSurface>();
        if (surface != null)
        {
            GUILayout.Label($"✅ NavMeshSurface found on: {surface.gameObject.name}");
        }
        else
        {
            GUILayout.Label("❌ No NavMeshSurface found");
        }

        var triangulation = UnityEngine.AI.NavMesh.CalculateTriangulation();
        if (triangulation.vertices.Length > 0)
        {
            GUILayout.Label($"✅ NavMesh baked ({triangulation.vertices.Length} vertices)");
        }
        else
        {
            GUILayout.Label("❌ NavMesh not baked");
        }
    }

    void AnalyzeSceneMeshes()
    {
        Debug.Log("🔍 Analyzing scene meshes for NavMesh compatibility...");

        MeshRenderer[] allRenderers = FindObjectsOfType<MeshRenderer>();
        int totalMeshes = 0;
        int problematicMeshes = 0;
        List<string> problematicNames = new List<string>();

        foreach (MeshRenderer renderer in allRenderers)
        {
            MeshFilter meshFilter = renderer.GetComponent<MeshFilter>();
            if (meshFilter != null && meshFilter.sharedMesh != null)
            {
                totalMeshes++;
                if (IsProblematicMesh(meshFilter.sharedMesh, renderer.gameObject.name))
                {
                    problematicMeshes++;
                    problematicNames.Add(renderer.gameObject.name);
                }
            }
        }

        Debug.Log($"📊 Mesh Analysis Complete:");
        Debug.Log($"   Total meshes: {totalMeshes}");
        Debug.Log($"   Problematic meshes: {problematicMeshes}");
        
        if (problematicNames.Count > 0)
        {
            Debug.Log($"   Problematic mesh names: {string.Join(", ", problematicNames.ToArray())}");
        }

        EditorUtility.DisplayDialog("Mesh Analysis Complete", 
            $"Analysis Results:\n\n" +
            $"Total meshes: {totalMeshes}\n" +
            $"Problematic meshes: {problematicMeshes}\n\n" +
            $"Check console for detailed list.", "OK");
    }

    void FixNavMeshIssues()
    {
        Debug.Log("🗺️ Fixing NavMesh issues...");

        // Find arena or create NavMesh target
        GameObject target = FindNavMeshTarget();
        if (target == null)
        {
            Debug.LogError("❌ No suitable target found for NavMesh building");
            return;
        }

        // Filter problematic meshes
        FilterProblematicMeshes(target);

        // Setup or get NavMeshSurface
        NavMeshSurface navSurface = target.GetComponent<NavMeshSurface>();
        if (navSurface == null)
        {
            navSurface = target.AddComponent<NavMeshSurface>();
        }

        // Configure optimized settings
        ConfigureNavMeshSurface(navSurface);

        // Build NavMesh
        try
        {
            navSurface.BuildNavMesh();
            
            // Verify success
            var triangulation = UnityEngine.AI.NavMesh.CalculateTriangulation();
            if (triangulation.vertices.Length > 0)
            {
                Debug.Log($"✅ NavMesh fixed successfully ({triangulation.vertices.Length} vertices)");
                EditorUtility.DisplayDialog("NavMesh Fixed", 
                    $"NavMesh has been successfully rebuilt!\n\n" +
                    $"Vertices: {triangulation.vertices.Length}\n" +
                    $"Filtered meshes: {filteredMeshes.Count}", "OK");
            }
            else
            {
                Debug.LogWarning("⚠️ NavMesh appears empty after building");
                SetupFallbackNavMesh(target);
            }
        }
        catch (System.Exception e)
        {
            Debug.LogWarning($"⚠️ NavMesh building failed: {e.Message}");
            SetupFallbackNavMesh(target);
        }
        finally
        {
            // Re-enable filtered meshes
            ReEnableFilteredMeshes();
        }
    }

    GameObject FindNavMeshTarget()
    {
        // Try to find arena objects
        GameObject target = GameObject.Find("TDM_Arena") ?? 
                           GameObject.Find("Tdm") ?? 
                           GameObject.Find("PUBG_TDM_Arena") ??
                           GameObject.Find("Arena");

        if (target == null)
        {
            // Look for any object with NavMeshSurface
            NavMeshSurface existing = FindObjectOfType<NavMeshSurface>();
            if (existing != null)
            {
                target = existing.gameObject;
            }
        }

        if (target == null)
        {
            // Create a new NavMesh target
            target = new GameObject("NavMesh_Target");
            Debug.Log("📍 Created new NavMesh target object");
        }

        return target;
    }

    void FilterProblematicMeshes(GameObject target)
    {
        Debug.Log("🔍 Filtering problematic meshes...");
        
        filteredMeshes.Clear();
        MeshRenderer[] renderers = target.GetComponentsInChildren<MeshRenderer>();
        int filteredCount = 0;
        
        foreach (MeshRenderer renderer in renderers)
        {
            MeshFilter meshFilter = renderer.GetComponent<MeshFilter>();
            if (meshFilter != null && meshFilter.sharedMesh != null)
            {
                if (IsProblematicMesh(meshFilter.sharedMesh, renderer.gameObject.name))
                {
                    filteredMeshes.Add(renderer.gameObject);
                    renderer.gameObject.SetActive(false);
                    filteredCount++;
                }
            }
        }
        
        Debug.Log($"🔍 Filtered {filteredCount} problematic meshes");
    }

    void ReEnableFilteredMeshes()
    {
        Debug.Log("🔄 Re-enabling filtered meshes...");
        
        foreach (GameObject mesh in filteredMeshes)
        {
            if (mesh != null)
            {
                mesh.SetActive(true);
            }
        }
        
        Debug.Log($"✅ Re-enabled {filteredMeshes.Count} filtered meshes");
        filteredMeshes.Clear();
    }

    bool IsProblematicMesh(Mesh mesh, string objectName)
    {
        // Check for known problematic mesh names from error logs
        string[] problematicNames = {
            "0.024", "0.025", "0.026", "0.027", "0.028", "0.029", "0.030", "0.031",
            "0.032", "0.033", "0.034", "0.035", "0.036", "0.037", "0.038", "0.039",
            "0.040", "0.041", "0.042", "0.043", "0.044", "0.045", "0.046", "0.047",
            "0.048", "0.049", "0.050", "0.051", "0.052", "0.053", "0.054", "0.055",
            "0.056", "0.057", "0.058", "0.059", "0.060", "0.061", "0.062", "0.063",
            "0.064", "0.065", "0.066", "0.067", "0.068", "0.069", "0.070", "0.071",
            "0.072", "0.073", "0.074", "0.075", "0.076", "0.077", "0.078", "0.079",
            "0.080", "0.081", "0.082", "0.083", "0.084", "0.085", "0.086", "0.087",
            "0.088", "0.089", "0.090", "0.091",
            "highcube", "MPG127", "png-transparent-irritation-hazard-symbol"
        };
        
        foreach (string problematicName in problematicNames)
        {
            if (objectName.Contains(problematicName))
            {
                return true;
            }
        }
        
        // Check for mesh validity issues
        if (mesh.vertices.Length == 0 || mesh.triangles.Length == 0)
        {
            return true;
        }
        
        // Check for invalid normals
        if (mesh.normals.Length > 0)
        {
            foreach (Vector3 normal in mesh.normals)
            {
                if (float.IsNaN(normal.x) || float.IsNaN(normal.y) || float.IsNaN(normal.z))
                {
                    return true;
                }
            }
        }
        
        return false;
    }

    void ConfigureNavMeshSurface(NavMeshSurface navSurface)
    {
        navSurface.collectObjects = CollectObjects.Children;
        navSurface.useGeometry = UnityEngine.AI.NavMeshCollectGeometry.RenderMeshes;
        navSurface.layerMask = useCustomLayerMask ? customLayerMask : GetWalkableLayerMask();
        navSurface.overrideVoxelSize = true;
        navSurface.voxelSize = voxelSize;
        navSurface.overrideTileSize = true;
        navSurface.tileSize = tileSize;
    }

    int GetWalkableLayerMask()
    {
        int layerMask = 0;
        layerMask |= (1 << LayerMask.NameToLayer("Default"));
        layerMask |= (1 << LayerMask.NameToLayer("Ground"));
        layerMask |= (1 << LayerMask.NameToLayer("Terrain"));
        return layerMask;
    }

    void SetupFallbackNavMesh(GameObject target)
    {
        Debug.Log("🔄 Setting up fallback NavMesh...");
        
        GameObject groundPlane = GameObject.CreatePrimitive(PrimitiveType.Plane);
        groundPlane.name = "NavMesh_FallbackGround";
        groundPlane.transform.parent = target.transform;
        groundPlane.transform.localPosition = Vector3.zero;
        groundPlane.transform.localScale = new Vector3(10, 1, 10);
        
        MeshRenderer planeRenderer = groundPlane.GetComponent<MeshRenderer>();
        if (planeRenderer != null)
        {
            planeRenderer.enabled = false;
        }
        
        NavMeshSurface navSurface = target.GetComponent<NavMeshSurface>();
        if (navSurface != null)
        {
            navSurface.BuildNavMesh();
            Debug.Log("✅ Fallback NavMesh created");
        }
    }

    void ClearNavMesh()
    {
        NavMeshSurface surface = FindObjectOfType<NavMeshSurface>();
        if (surface != null)
        {
            surface.RemoveData();
            Debug.Log("🧹 NavMesh data cleared");
        }
        else
        {
            Debug.Log("⚠️ No NavMeshSurface found to clear");
        }
    }

    void ExportMeshReport()
    {
        Debug.Log("📋 Exporting mesh compatibility report...");
        
        string report = "# NavMesh Mesh Compatibility Report\n\n";
        report += $"Generated: {System.DateTime.Now}\n\n";
        
        MeshRenderer[] allRenderers = FindObjectsOfType<MeshRenderer>();
        int totalMeshes = 0;
        int problematicMeshes = 0;
        
        report += "## Problematic Meshes:\n";
        
        foreach (MeshRenderer renderer in allRenderers)
        {
            MeshFilter meshFilter = renderer.GetComponent<MeshFilter>();
            if (meshFilter != null && meshFilter.sharedMesh != null)
            {
                totalMeshes++;
                if (IsProblematicMesh(meshFilter.sharedMesh, renderer.gameObject.name))
                {
                    problematicMeshes++;
                    report += $"- {renderer.gameObject.name} (Vertices: {meshFilter.sharedMesh.vertices.Length})\n";
                }
            }
        }
        
        report += $"\n## Summary:\n";
        report += $"- Total meshes: {totalMeshes}\n";
        report += $"- Problematic meshes: {problematicMeshes}\n";
        report += $"- Compatibility rate: {((float)(totalMeshes - problematicMeshes) / totalMeshes * 100):F1}%\n";
        
        string path = EditorUtility.SaveFilePanel("Save Mesh Report", "", "NavMesh_MeshReport.md", "md");
        if (!string.IsNullOrEmpty(path))
        {
            System.IO.File.WriteAllText(path, report);
            Debug.Log($"📋 Mesh report exported to: {path}");
            EditorUtility.DisplayDialog("Report Exported", $"Mesh compatibility report saved to:\n{path}", "OK");
        }
    }
}
