using UnityEngine;

/// <summary>
/// 🎮 Spectator UI for 5v5 TDM Matches
/// Displays match information, controls, and statistics during spectating
/// </summary>
public class SpectatorUI : MonoBehaviour
{
    [Header("📊 UI References")]
    public GameObject spectatorUI;
    public TextMesh matchInfoText;
    public TextMesh controlsText;
    public TextMesh leaderboardText;

    [Header("🎨 UI Settings")]
    public bool showUI = true;
    public float updateInterval = 1f;
    public Color teamAColor = Color.blue;
    public Color teamBColor = Color.red;

    private SquadManager squadManager;
    private SpectatorCamera spectatorCamera;
    private TournamentManager tournamentManager;
    private float lastUpdateTime;

    void Start()
    {
        // Find required components
        squadManager = FindObjectOfType<SquadManager>();
        spectatorCamera = FindObjectOfType<SpectatorCamera>();
        tournamentManager = FindObjectOfType<TournamentManager>();

        // Create UI if not assigned
        if (spectatorUI == null)
        {
            CreateSpectatorUI();
        }

        // Setup update interval
        lastUpdateTime = Time.time;

        Debug.Log("🎮 Spectator UI initialized");
    }

    void Update()
    {
        if (Time.time - lastUpdateTime >= updateInterval)
        {
            UpdateUI();
            lastUpdateTime = Time.time;
        }

        // Toggle UI with F12
        if (Input.GetKeyDown(KeyCode.F12))
        {
            ToggleUI();
        }
    }

    void CreateSpectatorUI()
    {
        // Create main UI container
        spectatorUI = new GameObject("SpectatorUI");

        // Create simple 3D text displays using OnGUI instead
        Debug.Log("🎨 Spectator UI created (using OnGUI)");
    }

    void OnGUI()
    {
        if (!showUI) return;

        // Set GUI skin for better appearance
        GUI.skin.box.normal.background = MakeTex(2, 2, new Color(0, 0, 0, 0.7f));
        GUI.skin.label.normal.textColor = Color.white;

        // Match Info Panel (top-left)
        GUI.Box(new Rect(10, 10, 300, 150), "");
        GUI.Label(new Rect(20, 20, 280, 130), GetMatchInfoText());

        // Controls Panel (bottom-left)
        GUI.Box(new Rect(10, Screen.height - 210, 350, 200), "");
        GUI.Label(new Rect(20, Screen.height - 200, 330, 180), GetControlsText());

        // Leaderboard Panel (top-right)
        GUI.Box(new Rect(Screen.width - 310, 10, 300, 400), "");
        GUI.Label(new Rect(Screen.width - 300, 20, 280, 380), GetLeaderboardText());

        // Toggle button (top-center)
        if (GUI.Button(new Rect(Screen.width / 2 - 60, 10, 120, 30), "Toggle UI (F12)"))
        {
            ToggleUI();
        }
    }

    Texture2D MakeTex(int width, int height, Color col)
    {
        Color[] pix = new Color[width * height];
        for (int i = 0; i < pix.Length; i++)
            pix[i] = col;
        Texture2D result = new Texture2D(width, height);
        result.SetPixels(pix);
        result.Apply();
        return result;
    }

    void UpdateUI()
    {
        // UI is now handled in OnGUI, no need for separate update
    }

    string GetMatchInfoText()
    {
        if (squadManager == null) return "No Match Data";

        var stats = squadManager.GetMatchStats();
        string cameraMode = spectatorCamera != null ? spectatorCamera.GetCurrentModeText() : "No Camera";

        string matchInfo = "5v5 TDM Match\n\n";
        matchInfo += $"Team A: {stats.teamAScore}\n";
        matchInfo += $"Team B: {stats.teamBScore}\n\n";
        matchInfo += $"Time: {stats.matchTime:F0}s\n";
        matchInfo += $"Agents: {stats.teamAAgents}v{stats.teamBAgents}\n";
        matchInfo += $"Status: {(stats.isActive ? "Active" : "Ended")}\n\n";
        matchInfo += $"Camera: {cameraMode}";

        return matchInfo;
    }

    string GetLeaderboardText()
    {
        if (tournamentManager == null) return "No Tournament Data";

        var topPlayers = tournamentManager.GetTopPlayers(8);

        string leaderboard = "Tournament Standings\n\n";

        for (int i = 0; i < topPlayers.Count; i++)
        {
            var player = topPlayers[i];
            string rankIcon = GetRankIcon(i + 1);

            leaderboard += $"{rankIcon} {player.name}\n";
            leaderboard += $"   ELO: {player.elo:F0} | {player.currentRank}\n";
            leaderboard += $"   W:{player.wins} L:{player.losses} | KDR:{player.KDR:F2}\n\n";
        }

        return leaderboard;
    }

    string GetRankIcon(int rank)
    {
        switch (rank)
        {
            case 1: return "🥇";
            case 2: return "🥈";
            case 3: return "🥉";
            default: return $"{rank}.";
        }
    }

    string GetControlsText()
    {
        return "Spectator Controls\n\n" +
               "Camera Modes:\n" +
               "F1 - Free Camera\n" +
               "F2 - Overview\n" +
               "F3 - Follow Agent\n" +
               "F4 - Combat Focus\n" +
               "F5 - Cinematic\n\n" +
               "Navigation:\n" +
               "Tab - Next Agent\n" +
               "Shift - Previous Agent\n" +
               "Mouse Wheel - Zoom\n\n" +
               "Free Camera:\n" +
               "WASD + QE - Move\n" +
               "Right Click + Mouse - Look\n\n" +
               "UI:\n" +
               "F12 - Toggle UI";
    }

    public void ToggleUI()
    {
        showUI = !showUI;
        Debug.Log($"🎮 Spectator UI: {(showUI ? "Shown" : "Hidden")}");
    }

    // Public methods for external control
    public void ShowMatchEndScreen(string winner, int teamAScore, int teamBScore)
    {
        // Match end info will be shown in the regular match info panel
        Debug.Log($"🏁 Match Ended - Winner: {winner}, Score: {teamAScore}-{teamBScore}");
    }

    void OnDestroy()
    {
        if (spectatorUI != null)
        {
            Destroy(spectatorUI);
        }
    }
}
