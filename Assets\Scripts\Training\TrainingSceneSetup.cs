using UnityEngine;
using UnityEngine.AI;
using Unity.AI.Navigation;
using System.Collections.Generic;

/// <summary>
/// Automated setup script for PUBG Arena Training Scene
/// Handles spawn points, zones, loot placement, and NavMesh configuration
/// </summary>
public class TrainingSceneSetup : MonoBehaviour
{
    [Header("🏟️ Arena Configuration")]
    public GameObject arenaPrefab; // TDM.fbx or your arena model
    public bool autoSetupNavMesh = true;
    public bool autoPlaceLoot = true;
    public bool autoCreateSpawns = true;

    [Header("👥 Team Spawns")]
    public int teamSize = 5;
    public float spawnRadius = 10f;
    public Vector3 teamASpawnCenter = new Vector3(-20, 0, 0);
    public Vector3 teamBSpawnCenter = new Vector3(20, 0, 0);

    [Header("🎯 Zone Configuration")]
    public Vector3[] zonePositions = {
        new Vector3(0, 0, 15),   // Zone Alpha
        new Vector3(-15, 0, 0), // Zone Bravo  
        new Vector3(15, 0, 0)   // Zone Charlie
    };
    public float zoneRadius = 8f;
    public float zoneHeight = 4f;

    [Header("📦 Loot Configuration")]
    public GameObject[] weaponPrefabs;
    public GameObject[] healingPrefabs;
    public int weaponsPerZone = 3;
    public int healingPerZone = 2;
    public float lootSpawnHeight = 1f;

    [Header("🛤️ Flank Routes")]
    public int flankPointsCount = 6;
    public float flankRouteRadius = 25f;

    [Header("🎮 Prefab References")]
    public GameObject squadMateAgentPrefab;
    public GameObject gameManagerPrefab;
    public GameObject leaderboardUIPrefab;

    private List<Transform> teamASpawns = new List<Transform>();
    private List<Transform> teamBSpawns = new List<Transform>();
    private List<Transform> zoneMarkers = new List<Transform>();

    [ContextMenu("Setup Training Scene")]
    public void SetupTrainingScene()
    {
        Debug.Log("🏗️ Setting up PUBG Arena Training Scene...");

        // 1. Setup Arena
        SetupArena();

        // 2. Create Team Spawns
        if (autoCreateSpawns)
        {
            CreateTeamSpawns();
        }

        // 3. Create Zones
        CreateZones();

        // 4. Setup Flank Routes
        CreateFlankRoutes();

        // 5. Place Loot
        if (autoPlaceLoot)
        {
            PlaceLoot();
        }

        // 6. Setup NavMesh
        if (autoSetupNavMesh)
        {
            SetupNavMesh();
        }

        // 7. Create Game Manager
        CreateGameManager();

        // 8. Setup UI
        SetupUI();

        Debug.Log("✅ Training scene setup complete!");
    }

    void SetupArena()
    {
        // First check if TDM arena already exists
        GameObject existingArena = GameObject.Find("Tdm") ?? GameObject.Find("TDM_Arena") ?? GameObject.Find("PUBG_TDM_Arena");
        if (existingArena != null)
        {
            Debug.Log("🏟️ Found existing TDM arena, fixing position...");
            FixArenaPosition(existingArena);
            return;
        }

        if (arenaPrefab != null)
        {
            GameObject arena = Instantiate(arenaPrefab, Vector3.zero, Quaternion.identity);
            arena.name = "TDM_Arena";

            // Fix arena position and scale
            FixArenaPosition(arena);

            // Make arena static for NavMesh baking
            MakeStaticRecursive(arena.transform);

            Debug.Log("🏟️ Arena instantiated and positioned correctly");
        }
        else
        {
            Debug.LogWarning("⚠️ No arena prefab assigned! Please assign the TDM.fbx model.");
        }
    }

    void FixArenaPosition(GameObject arena)
    {
        // Reset position to center of scene
        arena.transform.position = Vector3.zero;
        arena.transform.rotation = Quaternion.identity;

        // Ensure proper scale (TDM models are often too large)
        arena.transform.localScale = Vector3.one * 0.1f; // Scale down to 10% if too large

        // Check if arena has a collider and adjust bounds
        Collider arenaCollider = arena.GetComponent<Collider>();
        if (arenaCollider == null)
        {
            // Add a box collider for the entire arena
            BoxCollider boxCollider = arena.AddComponent<BoxCollider>();
            boxCollider.size = new Vector3(100f, 10f, 100f); // Adjust based on your arena size
        }

        Debug.Log($"✅ Arena positioned at {arena.transform.position} with scale {arena.transform.localScale}");
    }

    void CreateTeamSpawns()
    {
        // Create Team A spawns
        GameObject teamAParent = new GameObject("TeamASpawns");
        for (int i = 0; i < teamSize; i++)
        {
            Vector3 spawnPos = teamASpawnCenter + Random.insideUnitSphere * spawnRadius;
            spawnPos.y = 0; // Keep on ground level

            GameObject spawn = new GameObject($"TeamA_Spawn_{i + 1}");
            spawn.transform.position = spawnPos;
            spawn.transform.parent = teamAParent.transform;
            spawn.tag = "TeamASpawn";

            teamASpawns.Add(spawn.transform);
        }

        // Create Team B spawns
        GameObject teamBParent = new GameObject("TeamBSpawns");
        for (int i = 0; i < teamSize; i++)
        {
            Vector3 spawnPos = teamBSpawnCenter + Random.insideUnitSphere * spawnRadius;
            spawnPos.y = 0; // Keep on ground level

            GameObject spawn = new GameObject($"TeamB_Spawn_{i + 1}");
            spawn.transform.position = spawnPos;
            spawn.transform.parent = teamBParent.transform;
            spawn.tag = "TeamBSpawn";

            teamBSpawns.Add(spawn.transform);
        }

        Debug.Log($"👥 Created {teamSize} spawns for each team");
    }

    void CreateZones()
    {
        string[] zoneNames = { "Alpha", "Bravo", "Charlie" };

        for (int i = 0; i < zonePositions.Length && i < zoneNames.Length; i++)
        {
            GameObject zone = new GameObject($"Zone_{zoneNames[i]}");
            zone.transform.position = zonePositions[i];

            // Add collider for zone detection
            BoxCollider zoneCollider = zone.AddComponent<BoxCollider>();
            zoneCollider.isTrigger = true;
            zoneCollider.size = new Vector3(zoneRadius * 2, zoneHeight, zoneRadius * 2);

            // Add ZoneMarker script
            ZoneMarker zoneMarker = zone.AddComponent<ZoneMarker>();
            zoneMarker.zoneName = zoneNames[i];
            zoneMarker.zoneRadius = zoneRadius;

            // Visual indicator (optional)
            CreateZoneVisual(zone, zoneRadius);

            zoneMarkers.Add(zone.transform);
        }

        Debug.Log($"🎯 Created {zonePositions.Length} capture zones");
    }

    void CreateZoneVisual(GameObject zone, float radius)
    {
        GameObject visual = GameObject.CreatePrimitive(PrimitiveType.Cylinder);
        visual.name = "ZoneVisual";
        visual.transform.parent = zone.transform;
        visual.transform.localPosition = Vector3.zero;
        visual.transform.localScale = new Vector3(radius * 2, 0.1f, radius * 2);

        // Make it semi-transparent
        Renderer renderer = visual.GetComponent<Renderer>();
        Material mat = new Material(Shader.Find("Standard"));
        mat.color = new Color(0, 1, 0, 0.3f);
        mat.SetFloat("_Mode", 3); // Transparent mode
        renderer.material = mat;

        // Remove collider from visual
        Destroy(visual.GetComponent<Collider>());
    }

    void CreateFlankRoutes()
    {
        GameObject flankParent = new GameObject("FlankRoute");
        FlankRoute flankRoute = flankParent.AddComponent<FlankRoute>();

        List<Transform> flankPoints = new List<Transform>();

        for (int i = 0; i < flankPointsCount; i++)
        {
            float angle = (360f / flankPointsCount) * i * Mathf.Deg2Rad;
            Vector3 pos = new Vector3(
                Mathf.Cos(angle) * flankRouteRadius,
                0,
                Mathf.Sin(angle) * flankRouteRadius
            );

            GameObject flankPoint = new GameObject($"FlankPoint_{i + 1}");
            flankPoint.transform.position = pos;
            flankPoint.transform.parent = flankParent.transform;

            flankPoints.Add(flankPoint.transform);
        }

        flankRoute.waypoints = flankPoints;
        Debug.Log($"🛤️ Created flank route with {flankPointsCount} points");
    }

    void PlaceLoot()
    {
        foreach (Transform zone in zoneMarkers)
        {
            // Place weapons
            for (int i = 0; i < weaponsPerZone && weaponPrefabs.Length > 0; i++)
            {
                Vector3 lootPos = zone.position + Random.insideUnitSphere * (zoneRadius * 0.8f);
                lootPos.y = lootSpawnHeight;

                GameObject weaponPrefab = weaponPrefabs[Random.Range(0, weaponPrefabs.Length)];
                GameObject weapon = Instantiate(weaponPrefab, lootPos, Random.rotation);
                weapon.tag = "LootItem";
            }

            // Place healing items
            for (int i = 0; i < healingPerZone && healingPrefabs.Length > 0; i++)
            {
                Vector3 lootPos = zone.position + Random.insideUnitSphere * (zoneRadius * 0.8f);
                lootPos.y = lootSpawnHeight;

                GameObject healingPrefab = healingPrefabs[Random.Range(0, healingPrefabs.Length)];
                GameObject healing = Instantiate(healingPrefab, lootPos, Random.rotation);
                healing.tag = "LootItem";
            }
        }

        Debug.Log($"📦 Placed loot in {zoneMarkers.Count} zones");
    }

    void SetupNavMesh()
    {
        // Find or create NavMeshSurface
        NavMeshSurface navMeshSurface = FindObjectOfType<NavMeshSurface>();
        if (navMeshSurface == null)
        {
            GameObject navMeshObject = new GameObject("NavMeshSurface");
            navMeshSurface = navMeshObject.AddComponent<NavMeshSurface>();
        }

        // Configure NavMesh settings
        navMeshSurface.collectObjects = CollectObjects.All;
        navMeshSurface.useGeometry = UnityEngine.AI.NavMeshCollectGeometry.RenderMeshes;

        // Bake NavMesh
        navMeshSurface.BuildNavMesh();

        Debug.Log("🗺️ NavMesh baked successfully");
    }

    void CreateGameManager()
    {
        if (FindObjectOfType<SquadManager>() == null)
        {
            GameObject gameManager = new GameObject("GameManager");
            gameManager.AddComponent<SquadManager>();
            gameManager.AddComponent<RoleAssigner>();
            // gameManager.AddComponent<DecisionTreeLoader>(); // TODO: Implement DecisionTreeLoader

            Debug.Log("🎮 Game Manager created");
        }
    }

    void SetupUI()
    {
        if (leaderboardUIPrefab != null && FindObjectOfType<LeaderboardUI>() == null)
        {
            GameObject ui = Instantiate(leaderboardUIPrefab);
            ui.name = "LeaderboardUI";

            Debug.Log("📊 Leaderboard UI created");
        }
    }

    void MakeStaticRecursive(Transform parent)
    {
        parent.gameObject.isStatic = true;
        foreach (Transform child in parent)
        {
            MakeStaticRecursive(child);
        }
    }

    void OnDrawGizmosSelected()
    {
        // Draw team spawn areas
        Gizmos.color = Color.blue;
        Gizmos.DrawWireSphere(teamASpawnCenter, spawnRadius);

        Gizmos.color = Color.red;
        Gizmos.DrawWireSphere(teamBSpawnCenter, spawnRadius);

        // Draw zones
        Gizmos.color = Color.green;
        foreach (Vector3 zonePos in zonePositions)
        {
            Gizmos.DrawWireCube(zonePos, new Vector3(zoneRadius * 2, zoneHeight, zoneRadius * 2));
        }

        // Draw flank route
        Gizmos.color = Color.yellow;
        Gizmos.DrawWireSphere(Vector3.zero, flankRouteRadius);
    }
}
