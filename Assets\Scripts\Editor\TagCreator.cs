using UnityEngine;
using UnityEditor;

/// <summary>
/// Simple utility to create required tags for the Enhanced 5v5 system
/// </summary>
public class TagCreator
{
    [MenuItem("SquadMate AI/🏷️ Create Required Tags")]
    public static void CreateRequiredTags()
    {
        Debug.Log("🏷️ Creating required tags for Enhanced 5v5 system...");

        string[] requiredTags = { 
            "TeamASpawn", 
            "TeamBSpawn", 
            "TeamA", 
            "TeamB", 
            "SpawnPoint",
            "Enemy",
            "Medkit",
            "Weapon",
            "CaptureZone"
        };

        int createdCount = 0;
        int existingCount = 0;

        foreach (string tag in requiredTags)
        {
            if (!TagExists(tag))
            {
                AddTag(tag);
                Debug.Log($"✅ Created tag: {tag}");
                createdCount++;
            }
            else
            {
                Debug.Log($"ℹ️ Tag already exists: {tag}");
                existingCount++;
            }
        }

        string message = $"Tag Creation Complete!\n\n" +
                        $"Created: {createdCount} new tags\n" +
                        $"Existing: {existingCount} tags\n" +
                        $"Total: {requiredTags.Length} tags verified\n\n" +
                        $"You can now run the Enhanced 5v5 Setup without tag errors.";

        EditorUtility.DisplayDialog("Tags Created", message, "OK");
        Debug.Log($"✅ Tag creation complete: {createdCount} created, {existingCount} existing");
    }

    static bool TagExists(string tag)
    {
        try
        {
            SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
            SerializedProperty tagsProp = tagManager.FindProperty("tags");

            for (int i = 0; i < tagsProp.arraySize; i++)
            {
                SerializedProperty t = tagsProp.GetArrayElementAtIndex(i);
                if (t.stringValue.Equals(tag)) return true;
            }
            return false;
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error checking tag existence: {e.Message}");
            return false;
        }
    }

    static void AddTag(string tag)
    {
        try
        {
            SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
            SerializedProperty tagsProp = tagManager.FindProperty("tags");

            // Add tag
            tagsProp.InsertArrayElementAtIndex(tagsProp.arraySize);
            SerializedProperty newTagProp = tagsProp.GetArrayElementAtIndex(tagsProp.arraySize - 1);
            newTagProp.stringValue = tag;

            tagManager.ApplyModifiedProperties();
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error adding tag '{tag}': {e.Message}");
        }
    }

    [MenuItem("SquadMate AI/🔍 List All Tags")]
    public static void ListAllTags()
    {
        Debug.Log("🔍 Listing all existing tags...");

        try
        {
            SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
            SerializedProperty tagsProp = tagManager.FindProperty("tags");

            System.Text.StringBuilder sb = new System.Text.StringBuilder();
            sb.AppendLine("Current Tags:");

            for (int i = 0; i < tagsProp.arraySize; i++)
            {
                SerializedProperty t = tagsProp.GetArrayElementAtIndex(i);
                if (!string.IsNullOrEmpty(t.stringValue))
                {
                    sb.AppendLine($"  • {t.stringValue}");
                    Debug.Log($"Tag {i}: {t.stringValue}");
                }
            }

            EditorUtility.DisplayDialog("All Tags", sb.ToString(), "OK");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error listing tags: {e.Message}");
        }
    }

    [MenuItem("SquadMate AI/🧹 Clean Empty Tags")]
    public static void CleanEmptyTags()
    {
        Debug.Log("🧹 Cleaning empty tags...");

        try
        {
            SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
            SerializedProperty tagsProp = tagManager.FindProperty("tags");

            int removedCount = 0;

            // Remove empty tags (iterate backwards to avoid index issues)
            for (int i = tagsProp.arraySize - 1; i >= 0; i--)
            {
                SerializedProperty t = tagsProp.GetArrayElementAtIndex(i);
                if (string.IsNullOrEmpty(t.stringValue))
                {
                    tagsProp.DeleteArrayElementAtIndex(i);
                    removedCount++;
                }
            }

            tagManager.ApplyModifiedProperties();

            string message = $"Cleaned {removedCount} empty tag slots.";
            EditorUtility.DisplayDialog("Tags Cleaned", message, "OK");
            Debug.Log($"✅ {message}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"Error cleaning tags: {e.Message}");
        }
    }
}
