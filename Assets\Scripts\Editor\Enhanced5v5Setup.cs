using UnityEngine;
using UnityEditor;
using Unity.AI.Navigation;
using System.Collections.Generic;

/// <summary>
/// 🚀 Enhanced 5v5 TDM Setup with All Features
/// Complete setup for 5v5 matches with spectator mode, match logging, and tournament system
/// </summary>
public class Enhanced5v5Setup : EditorWindow
{
    private List<GameObject> filteredMeshes = new List<GameObject>();
    [Header("🏟️ Arena Setup")]
    public GameObject tdmArenaPrefab;
    public bool fixArenaPosition = true;
    public Vector3 arenaScale = new Vector3(0.1f, 0.1f, 0.1f);

    [Header("🤖 Agent Setup")]
    public GameObject victorAgentPrefab;
    public int teamSize = 5;

    [Header("📹 Camera Setup")]
    public bool setupSpectatorCamera = true;
    public Vector3 spectatorStartPosition = new Vector3(0, 15, -20);

    [MenuItem("SquadMate AI/🚀 Enhanced 5v5 Setup")]
    public static void ShowWindow()
    {
        Enhanced5v5Setup window = GetWindow<Enhanced5v5Setup>("Enhanced 5v5 Setup");
        window.minSize = new Vector2(400, 600);
        window.Show();
        Debug.Log("🚀 Enhanced 5v5 Setup window opened");
    }

    void OnGUI()
    {
        GUILayout.Label("🚀 Enhanced 5v5 TDM Setup", EditorStyles.boldLabel);
        GUILayout.Space(10);

        EditorGUILayout.HelpBox("This will set up a complete 5v5 TDM environment with:\n" +
                               "• Fixed TDM arena positioning\n" +
                               "• 5v5 AI agent teams\n" +
                               "• Spectator camera system\n" +
                               "• Match logging & statistics\n" +
                               "• Tournament & ELO system\n" +
                               "• Agent tooltips", MessageType.Info);

        GUILayout.Space(10);

        // Arena Setup
        GUILayout.Label("🏟️ Arena Configuration", EditorStyles.boldLabel);
        tdmArenaPrefab = EditorGUILayout.ObjectField("TDM Arena Prefab", tdmArenaPrefab, typeof(GameObject), false) as GameObject;
        fixArenaPosition = EditorGUILayout.Toggle("Fix Arena Position", fixArenaPosition);
        arenaScale = EditorGUILayout.Vector3Field("Arena Scale", arenaScale);

        GUILayout.Space(10);

        // Agent Setup
        GUILayout.Label("🤖 Agent Configuration", EditorStyles.boldLabel);
        victorAgentPrefab = EditorGUILayout.ObjectField("Victor Agent Prefab", victorAgentPrefab, typeof(GameObject), false) as GameObject;
        teamSize = EditorGUILayout.IntSlider("Team Size", teamSize, 1, 10);

        GUILayout.Space(10);

        // Camera Setup
        GUILayout.Label("📹 Camera Configuration", EditorStyles.boldLabel);
        setupSpectatorCamera = EditorGUILayout.Toggle("Setup Spectator Camera", setupSpectatorCamera);
        spectatorStartPosition = EditorGUILayout.Vector3Field("Camera Start Position", spectatorStartPosition);

        GUILayout.Space(20);

        // Setup Buttons
        if (GUILayout.Button("🏷️ Create Required Tags", GUILayout.Height(30)))
        {
            CreateRequiredTags();
        }

        if (GUILayout.Button("🔧 Fix TDM Arena Position Only", GUILayout.Height(30)))
        {
            FixTDMArenaPosition();
        }

        if (GUILayout.Button("🚀 Complete 5v5 Setup", GUILayout.Height(40)))
        {
            SetupComplete5v5Environment();
        }

        GUILayout.Space(10);

        if (GUILayout.Button("📦 Create Unity Package", GUILayout.Height(30)))
        {
            CreateUnityPackage();
        }

        GUILayout.Space(10);

        // Status
        ShowCurrentStatus();
    }

    void CreateRequiredTags()
    {
        Debug.Log("🏷️ Creating required tags...");

        string[] requiredTags = { "TeamASpawn", "TeamBSpawn", "TeamA", "TeamB", "SpawnPoint" };

        foreach (string tag in requiredTags)
        {
            if (!TagExists(tag))
            {
                AddTag(tag);
                Debug.Log($"✅ Created tag: {tag}");
            }
        }

        Debug.Log("✅ All required tags verified");
    }

    bool TagExists(string tag)
    {
        SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
        SerializedProperty tagsProp = tagManager.FindProperty("tags");

        for (int i = 0; i < tagsProp.arraySize; i++)
        {
            SerializedProperty t = tagsProp.GetArrayElementAtIndex(i);
            if (t.stringValue.Equals(tag)) return true;
        }
        return false;
    }

    void AddTag(string tag)
    {
        SerializedObject tagManager = new SerializedObject(AssetDatabase.LoadAllAssetsAtPath("ProjectSettings/TagManager.asset")[0]);
        SerializedProperty tagsProp = tagManager.FindProperty("tags");

        // Add tag
        tagsProp.InsertArrayElementAtIndex(tagsProp.arraySize);
        SerializedProperty newTagProp = tagsProp.GetArrayElementAtIndex(tagsProp.arraySize - 1);
        newTagProp.stringValue = tag;

        tagManager.ApplyModifiedProperties();
    }

    void FixTDMArenaPosition()
    {
        Debug.Log("🔧 Fixing TDM arena position...");

        // Find existing TDM arena
        GameObject arena = GameObject.Find("Tdm") ?? GameObject.Find("TDM_Arena") ?? GameObject.Find("PUBG_TDM_Arena");

        if (arena == null && tdmArenaPrefab != null)
        {
            // Create new arena if none exists
            arena = PrefabUtility.InstantiatePrefab(tdmArenaPrefab) as GameObject;
            arena.name = "TDM_Arena";
            Debug.Log("✅ Created new TDM arena from prefab");
        }

        if (arena != null)
        {
            // Fix position and scale
            arena.transform.position = Vector3.zero;
            arena.transform.rotation = Quaternion.identity;
            arena.transform.localScale = arenaScale;

            // Ensure it's marked as static for NavMesh
            MakeStaticRecursive(arena.transform);

            Debug.Log($"✅ TDM arena positioned at {arena.transform.position} with scale {arena.transform.localScale}");
        }
        else
        {
            Debug.LogError("❌ No TDM arena found and no prefab assigned!");
        }
    }

    void SetupComplete5v5Environment()
    {
        Debug.Log("🚀 Setting up complete 5v5 TDM environment...");

        // Step 0: Create required tags
        CreateRequiredTags();

        // Step 1: Fix arena position
        FixTDMArenaPosition();

        // Step 2: Setup spawn points
        SetupSpawnPoints();

        // Step 3: Setup game manager with enhanced systems
        SetupGameManager();

        // Step 4: Setup spectator camera
        if (setupSpectatorCamera)
        {
            SetupSpectatorCameraSystem();
        }

        // Step 5: Setup NavMesh
        SetupNavMesh();

        // Step 5.1: Re-enable filtered meshes
        ReEnableFilteredMeshes();

        // Step 6: Add tooltips to existing agents
        AddTooltipsToExistingAgents();

        Debug.Log("✅ Complete 5v5 TDM environment setup finished!");
        EditorUtility.DisplayDialog("Setup Complete",
            "Enhanced 5v5 TDM environment has been set up!\n\n" +
            "Features added:\n" +
            "• Fixed TDM arena positioning\n" +
            "• 5v5 spawn points\n" +
            "• Enhanced SquadManager\n" +
            "• Match logging system\n" +
            "• Tournament & ELO tracking\n" +
            "• Spectator camera\n" +
            "• Agent tooltips\n\n" +
            "Press Play to start the match!", "OK");
    }

    void SetupSpawnPoints()
    {
        Debug.Log("📍 Setting up 5v5 spawn points...");

        GameObject spawnParent = GameObject.Find("SpawnPoints");
        if (spawnParent == null)
        {
            spawnParent = new GameObject("SpawnPoints");
        }

        // Team A spawn points (left side)
        GameObject teamASpawns = new GameObject("TeamA_Spawns");
        teamASpawns.transform.SetParent(spawnParent.transform);

        Vector3[] teamAPositions = {
            new Vector3(-25, 0, 20),
            new Vector3(-30, 0, 15),
            new Vector3(-25, 0, 10),
            new Vector3(-30, 0, 5),
            new Vector3(-25, 0, 0)
        };

        for (int i = 0; i < teamAPositions.Length; i++)
        {
            GameObject spawn = new GameObject($"TeamA_Spawn_{i}");
            spawn.transform.SetParent(teamASpawns.transform);
            spawn.transform.position = teamAPositions[i];
            spawn.tag = "TeamASpawn";
        }

        // Team B spawn points (right side)
        GameObject teamBSpawns = new GameObject("TeamB_Spawns");
        teamBSpawns.transform.SetParent(spawnParent.transform);

        Vector3[] teamBPositions = {
            new Vector3(25, 0, 20),
            new Vector3(30, 0, 15),
            new Vector3(25, 0, 10),
            new Vector3(30, 0, 5),
            new Vector3(25, 0, 0)
        };

        for (int i = 0; i < teamBPositions.Length; i++)
        {
            GameObject spawn = new GameObject($"TeamB_Spawn_{i}");
            spawn.transform.SetParent(teamBSpawns.transform);
            spawn.transform.position = teamBPositions[i];
            spawn.tag = "TeamBSpawn";
        }

        Debug.Log("✅ 5v5 spawn points created");
    }

    void SetupGameManager()
    {
        Debug.Log("🎮 Setting up enhanced game manager...");

        GameObject gameManager = GameObject.Find("GameManager");
        if (gameManager == null)
        {
            gameManager = new GameObject("GameManager");
        }

        // Add SquadManager
        SquadManager squadManager = gameManager.GetComponent<SquadManager>();
        if (squadManager == null)
        {
            squadManager = gameManager.AddComponent<SquadManager>();
        }

        // Configure for 5v5
        squadManager.teamSize = teamSize;
        squadManager.victorAgentPrefab = victorAgentPrefab;

        // Setup spawn point references
        SetupSpawnPointReferences(squadManager);

        Debug.Log("✅ Enhanced game manager configured");
    }

    void SetupSpawnPointReferences(SquadManager squadManager)
    {
        // Find spawn points
        Transform[] teamASpawns = GameObject.Find("TeamA_Spawns")?.GetComponentsInChildren<Transform>();
        Transform[] teamBSpawns = GameObject.Find("TeamB_Spawns")?.GetComponentsInChildren<Transform>();

        if (teamASpawns != null && teamASpawns.Length > 1)
        {
            // Remove parent transform from array
            Transform[] teamASpawnPoints = new Transform[teamASpawns.Length - 1];
            for (int i = 1; i < teamASpawns.Length; i++)
            {
                teamASpawnPoints[i - 1] = teamASpawns[i];
            }
            squadManager.teamASpawnPoints = teamASpawnPoints;
        }

        if (teamBSpawns != null && teamBSpawns.Length > 1)
        {
            // Remove parent transform from array
            Transform[] teamBSpawnPoints = new Transform[teamBSpawns.Length - 1];
            for (int i = 1; i < teamBSpawns.Length; i++)
            {
                teamBSpawnPoints[i - 1] = teamBSpawns[i];
            }
            squadManager.teamBSpawnPoints = teamBSpawnPoints;
        }
    }

    void SetupSpectatorCameraSystem()
    {
        Debug.Log("📹 Setting up spectator camera...");

        Camera mainCam = Camera.main;
        if (mainCam == null)
        {
            mainCam = FindObjectOfType<Camera>();
        }

        if (mainCam == null)
        {
            // Create new camera
            GameObject camObj = new GameObject("SpectatorCamera");
            mainCam = camObj.AddComponent<Camera>();
            camObj.tag = "MainCamera";
            Debug.Log("📹 Created new main camera");
        }

        // Add SpectatorCamera component
        SpectatorCamera spectatorCam = mainCam.GetComponent<SpectatorCamera>();
        if (spectatorCam == null)
        {
            spectatorCam = mainCam.gameObject.AddComponent<SpectatorCamera>();
            Debug.Log("📹 Added SpectatorCamera component");
        }

        // Position camera
        mainCam.transform.position = spectatorStartPosition;
        mainCam.transform.LookAt(Vector3.zero);

        Debug.Log("✅ Spectator camera system ready");
    }

    void SetupNavMesh()
    {
        Debug.Log("🗺️ Setting up NavMesh with improved mesh filtering...");

        // Find arena
        GameObject arena = GameObject.Find("TDM_Arena") ?? GameObject.Find("Tdm");
        if (arena != null)
        {
            // Pre-process: Filter out problematic meshes
            FilterProblematicMeshes(arena);

            NavMeshSurface navSurface = arena.GetComponent<NavMeshSurface>();
            if (navSurface == null)
            {
                navSurface = arena.AddComponent<NavMeshSurface>();
            }

            // Configure NavMesh settings optimized for complex imported geometry
            navSurface.collectObjects = CollectObjects.Children;
            navSurface.useGeometry = UnityEngine.AI.NavMeshCollectGeometry.RenderMeshes;
            navSurface.layerMask = GetWalkableLayerMask(); // Only include walkable surfaces
            navSurface.overrideVoxelSize = true;
            navSurface.voxelSize = 0.2f; // Larger voxel size for stability
            navSurface.overrideTileSize = true;
            navSurface.tileSize = 256; // Smaller tiles for better handling

            try
            {
                navSurface.BuildNavMesh();

                // Verify NavMesh was built successfully
                var triangulation = UnityEngine.AI.NavMesh.CalculateTriangulation();
                if (triangulation.vertices.Length > 0)
                {
                    Debug.Log($"✅ NavMesh baked successfully ({triangulation.vertices.Length} vertices)");
                }
                else
                {
                    Debug.LogWarning("⚠️ NavMesh appears empty after baking");
                }
            }
            catch (System.Exception e)
            {
                Debug.LogWarning($"⚠️ NavMesh baking had issues: {e.Message}");
                Debug.Log("🗺️ Attempting fallback NavMesh setup...");
                SetupFallbackNavMesh(arena);
            }
        }
        else
        {
            Debug.LogWarning("⚠️ No arena found for NavMesh setup");
        }
    }

    void FilterProblematicMeshes(GameObject arena)
    {
        Debug.Log("🔍 Filtering problematic meshes for NavMesh...");

        filteredMeshes.Clear(); // Clear previous list
        MeshRenderer[] renderers = arena.GetComponentsInChildren<MeshRenderer>();
        int filteredCount = 0;

        foreach (MeshRenderer renderer in renderers)
        {
            MeshFilter meshFilter = renderer.GetComponent<MeshFilter>();
            if (meshFilter != null && meshFilter.sharedMesh != null)
            {
                Mesh mesh = meshFilter.sharedMesh;

                // Check for problematic mesh characteristics
                if (IsProblematicMesh(mesh, renderer.gameObject.name))
                {
                    // Store reference and temporarily disable for NavMesh building
                    filteredMeshes.Add(renderer.gameObject);
                    renderer.gameObject.SetActive(false);
                    filteredCount++;
                }
            }
        }

        Debug.Log($"🔍 Filtered {filteredCount} problematic meshes");
    }

    void ReEnableFilteredMeshes()
    {
        Debug.Log("🔄 Re-enabling filtered meshes...");

        foreach (GameObject mesh in filteredMeshes)
        {
            if (mesh != null)
            {
                mesh.SetActive(true);
            }
        }

        Debug.Log($"✅ Re-enabled {filteredMeshes.Count} filtered meshes");
        filteredMeshes.Clear();
    }

    bool IsProblematicMesh(Mesh mesh, string objectName)
    {
        // Check for known problematic mesh names
        string[] problematicNames = {
            "0.024", "0.025", "0.026", "0.027", "0.028", "0.029", "0.030", "0.031",
            "0.032", "0.033", "0.034", "0.035", "0.036", "0.037", "0.038", "0.039",
            "0.040", "0.041", "0.042", "0.043", "0.044", "0.045", "0.046", "0.047",
            "0.048", "0.049", "0.050", "0.051", "0.052", "0.053", "0.054", "0.055",
            "0.056", "0.057", "0.058", "0.059", "0.060", "0.061", "0.062", "0.063",
            "0.064", "0.065", "0.066", "0.067", "0.068", "0.069", "0.070", "0.071",
            "0.072", "0.073", "0.074", "0.075", "0.076", "0.077", "0.078", "0.079",
            "0.080", "0.081", "0.082", "0.083", "0.084", "0.085", "0.086", "0.087",
            "0.088", "0.089", "0.090", "0.091",
            "highcube", "MPG127", "png-transparent-irritation-hazard-symbol"
        };

        foreach (string problematicName in problematicNames)
        {
            if (objectName.Contains(problematicName))
            {
                return true;
            }
        }

        // Check for mesh validity issues
        if (mesh.vertices.Length == 0 || mesh.triangles.Length == 0)
        {
            return true;
        }

        // Check for degenerate triangles or invalid normals
        if (mesh.normals.Length > 0)
        {
            foreach (Vector3 normal in mesh.normals)
            {
                if (float.IsNaN(normal.x) || float.IsNaN(normal.y) || float.IsNaN(normal.z))
                {
                    return true;
                }
            }
        }

        return false;
    }

    int GetWalkableLayerMask()
    {
        // Create layer mask for walkable surfaces only
        int layerMask = 0;
        layerMask |= (1 << LayerMask.NameToLayer("Default"));
        layerMask |= (1 << LayerMask.NameToLayer("Ground"));
        layerMask |= (1 << LayerMask.NameToLayer("Terrain"));

        return layerMask;
    }

    void SetupFallbackNavMesh(GameObject arena)
    {
        Debug.Log("🔄 Setting up fallback NavMesh...");

        // Create a simple ground plane for basic navigation
        GameObject groundPlane = GameObject.CreatePrimitive(PrimitiveType.Plane);
        groundPlane.name = "NavMesh_GroundPlane";
        groundPlane.transform.parent = arena.transform;
        groundPlane.transform.localPosition = Vector3.zero;
        groundPlane.transform.localScale = new Vector3(10, 1, 10);

        // Make it invisible but navigable
        MeshRenderer planeRenderer = groundPlane.GetComponent<MeshRenderer>();
        if (planeRenderer != null)
        {
            planeRenderer.enabled = false;
        }

        // Try building NavMesh again with just the ground plane
        NavMeshSurface navSurface = arena.GetComponent<NavMeshSurface>();
        if (navSurface != null)
        {
            navSurface.collectObjects = CollectObjects.Children;
            navSurface.BuildNavMesh();
            Debug.Log("✅ Fallback NavMesh created");
        }
    }

    void AddTooltipsToExistingAgents()
    {
        Debug.Log("💡 Adding tooltips to existing agents...");

        VictorAgent[] agents = FindObjectsOfType<VictorAgent>();
        foreach (var agent in agents)
        {
            if (agent.GetComponent<SimpleAgentTooltip>() == null)
            {
                agent.gameObject.AddComponent<SimpleAgentTooltip>();
            }
        }

        Debug.Log($"✅ Added tooltips to {agents.Length} agents");
    }

    void MakeStaticRecursive(Transform parent)
    {
        parent.gameObject.isStatic = true;
        foreach (Transform child in parent)
        {
            MakeStaticRecursive(child);
        }
    }

    void CreateUnityPackage()
    {
        Debug.Log("📦 Creating Unity package...");

        string[] assetPaths = {
            "Assets/Scripts",
            "Assets/Prefabs",
            "Assets/Materials",
            "Assets/Scenes/TrainingEnvironment.unity"
        };

        string packagePath = EditorUtility.SaveFilePanel(
            "Save Unity Package",
            "",
            "SquadMate-Enhanced-5v5-TDM.unitypackage",
            "unitypackage"
        );

        if (!string.IsNullOrEmpty(packagePath))
        {
            AssetDatabase.ExportPackage(assetPaths, packagePath, ExportPackageOptions.Recurse);
            Debug.Log($"✅ Unity package created: {packagePath}");
            EditorUtility.DisplayDialog("Package Created", $"Unity package saved to:\n{packagePath}", "OK");
        }
    }

    void ShowCurrentStatus()
    {
        GUILayout.Label("📊 Current Status", EditorStyles.boldLabel);

        // Check arena
        GameObject arena = GameObject.Find("Tdm") ?? GameObject.Find("TDM_Arena") ?? GameObject.Find("PUBG_TDM_Arena");
        GUILayout.Label($"🏟️ Arena: {(arena != null ? "✅ Found" : "❌ Missing")}");

        // Check spawn points
        GameObject spawns = GameObject.Find("SpawnPoints");
        GUILayout.Label($"📍 Spawn Points: {(spawns != null ? "✅ Found" : "❌ Missing")}");

        // Check game manager
        SquadManager squadManager = FindObjectOfType<SquadManager>();
        GUILayout.Label($"🎮 Squad Manager: {(squadManager != null ? "✅ Found" : "❌ Missing")}");

        // Check spectator camera
        SpectatorCamera spectatorCam = FindObjectOfType<SpectatorCamera>();
        GUILayout.Label($"📹 Spectator Camera: {(spectatorCam != null ? "✅ Found" : "❌ Missing")}");

        // Check enhanced systems
        MatchLogger matchLogger = FindObjectOfType<MatchLogger>();
        TournamentManager tournamentManager = FindObjectOfType<TournamentManager>();
        GUILayout.Label($"📊 Match Logger: {(matchLogger != null ? "✅ Found" : "❌ Missing")}");
        GUILayout.Label($"🏆 Tournament Manager: {(tournamentManager != null ? "✅ Found" : "❌ Missing")}");
    }
}
