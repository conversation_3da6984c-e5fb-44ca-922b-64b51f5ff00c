using UnityEngine;
using UnityEditor;

/// <summary>
/// Simple verification script to test menu items
/// </summary>
public class MenuVerification
{
    [MenuItem("SquadMate AI/🔧 Test Menu")]
    public static void TestMenu()
    {
        Debug.Log("✅ SquadMate AI menu is working!");
        EditorUtility.DisplayDialog("Menu Test", "SquadMate AI menu is working correctly!", "OK");
    }

    [MenuItem("SquadMate AI/🔧 Open Enhanced 5v5 Setup")]
    public static void OpenEnhanced5v5Setup()
    {
        Enhanced5v5Setup.ShowWindow();
    }

    [MenuItem("SquadMate AI/🔧 Verify Setup")]
    public static void VerifySetup()
    {
        Debug.Log("🔍 Verifying Enhanced 5v5 Setup...");

        // Check if Enhanced5v5Setup class exists
        System.Type setupType = System.Type.GetType("Enhanced5v5Setup");
        if (setupType != null)
        {
            Debug.Log("✅ Enhanced5v5Setup class found");
        }
        else
        {
            Debug.LogError("❌ Enhanced5v5Setup class not found");
        }

        // Check if other required classes exist
        string[] requiredClasses = {
            "MatchLogger",
            "TournamentManager", 
            "SpectatorCamera",
            "SpectatorUI",
            "SimpleAgentTooltip",
            "SquadManager",
            "VictorAgent"
        };

        int foundCount = 0;
        foreach (string className in requiredClasses)
        {
            System.Type classType = System.Type.GetType(className);
            if (classType != null)
            {
                Debug.Log($"✅ {className} found");
                foundCount++;
            }
            else
            {
                Debug.LogWarning($"⚠️ {className} not found");
            }
        }

        string message = $"Verification Complete!\n\n" +
                        $"Found {foundCount}/{requiredClasses.Length} required classes.\n\n" +
                        $"If Enhanced 5v5 Setup menu item is missing:\n" +
                        $"1. Check Console for compilation errors\n" +
                        $"2. Try reimporting the Scripts folder\n" +
                        $"3. Restart Unity Editor";

        EditorUtility.DisplayDialog("Setup Verification", message, "OK");
    }
}
