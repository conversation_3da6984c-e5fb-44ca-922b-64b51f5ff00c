{"gameMode": {"name": "Team Deathmatch 5v5", "objective": "First team to reach 40 kills or highest in 10 minutes", "maxKills": 40, "timeLimit": 600, "teamSize": 5, "respawnDelay": 3.0, "spawnProtectionTime": 3.0}, "map": {"name": "Warehouse Arena", "type": "Indoor Symmetric", "size": {"width": 100, "length": 80, "height": 15}, "lanes": ["left", "center", "right"], "spawnZones": {"teamA": {"positions": [{"x": -40, "y": 0, "z": 30}, {"x": -35, "y": 0, "z": 25}, {"x": -40, "y": 0, "z": 20}, {"x": -35, "y": 0, "z": 15}, {"x": -40, "y": 0, "z": 10}], "safeZone": {"x": -40, "y": 0, "z": 20, "radius": 15}}, "teamB": {"positions": [{"x": 40, "y": 0, "z": 30}, {"x": 35, "y": 0, "z": 25}, {"x": 40, "y": 0, "z": 20}, {"x": 35, "y": 0, "z": 15}, {"x": 40, "y": 0, "z": 10}], "safeZone": {"x": 40, "y": 0, "z": 20, "radius": 15}}}}, "weapons": {"assaultRifles": {"M416": {"damage": 43, "fireRate": 0.086, "range": 50, "accuracy": 0.85, "recoil": 0.3, "ammoType": "5.56mm", "magazineSize": 30, "reloadTime": 2.3}, "AKM": {"damage": 49, "fireRate": 0.1, "range": 45, "accuracy": 0.75, "recoil": 0.5, "ammoType": "7.62mm", "magazineSize": 30, "reloadTime": 2.5}, "SCAR-L": {"damage": 41, "fireRate": 0.096, "range": 48, "accuracy": 0.8, "recoil": 0.25, "ammoType": "5.56mm", "magazineSize": 30, "reloadTime": 2.2}}, "submachineGuns": {"UMP45": {"damage": 35, "fireRate": 0.092, "range": 25, "accuracy": 0.7, "recoil": 0.2, "ammoType": ".45ACP", "magazineSize": 25, "reloadTime": 2.0}, "Vector": {"damage": 31, "fireRate": 0.055, "range": 20, "accuracy": 0.65, "recoil": 0.4, "ammoType": ".45ACP", "magazineSize": 19, "reloadTime": 1.8}, "Uzi": {"damage": 26, "fireRate": 0.048, "range": 18, "accuracy": 0.6, "recoil": 0.35, "ammoType": "9mm", "magazineSize": 25, "reloadTime": 1.5}}, "sniperRifles": {"Kar98k": {"damage": 79, "fireRate": 1.9, "range": 100, "accuracy": 0.95, "recoil": 0.8, "ammoType": "7.62mm", "magazineSize": 5, "reloadTime": 3.5}}}, "playerStats": {"health": 100, "maxHealth": 100, "movementSpeed": 6.0, "sprintSpeed": 9.0, "crouchSpeed": 3.0, "proneSpeed": 1.5, "jumpHeight": 1.2, "slideDistance": 3.0}, "damageMultipliers": {"headshot": 2.0, "chest": 1.0, "limbs": 0.5}, "aiRoles": {"assault": {"description": "Aggressive front-line fighter", "preferredWeapons": ["M416", "AKM", "Vector"], "behavior": {"aggression": 0.8, "teamwork": 0.6, "positioning": "front", "engagementRange": "close-medium"}, "tactics": ["rush", "flank", "suppress"]}, "support": {"description": "Team support and area control", "preferredWeapons": ["SCAR-L", "UMP45"], "behavior": {"aggression": 0.5, "teamwork": 0.9, "positioning": "mid", "engagementRange": "medium"}, "tactics": ["cover", "revive", "suppress"]}, "sniper": {"description": "Long-range elimination specialist", "preferredWeapons": ["Kar98k", "M416"], "behavior": {"aggression": 0.3, "teamwork": 0.7, "positioning": "back", "engagementRange": "long"}, "tactics": ["overwatch", "pick", "fallback"]}, "scout": {"description": "Fast flanker and information gatherer", "preferredWeapons": ["Vector", "<PERSON><PERSON>"], "behavior": {"aggression": 0.7, "teamwork": 0.5, "positioning": "flank", "engagementRange": "close"}, "tactics": ["flank", "scout", "distract"]}, "anchor": {"description": "Defensive position holder", "preferredWeapons": ["AKM", "SCAR-L"], "behavior": {"aggression": 0.4, "teamwork": 0.8, "positioning": "defensive", "engagementRange": "medium-long"}, "tactics": ["hold", "defend", "crossfire"]}}, "coverPoints": {"leftLane": [{"x": -30, "y": 0, "z": 10, "type": "crate", "height": 1.5}, {"x": -25, "y": 0, "z": 5, "type": "barrel", "height": 1.0}, {"x": -20, "y": 0, "z": 0, "type": "wall", "height": 2.0}], "centerLane": [{"x": 0, "y": 0, "z": 10, "type": "pillar", "height": 3.0}, {"x": -5, "y": 0, "z": 0, "type": "crate", "height": 1.5}, {"x": 5, "y": 0, "z": 0, "type": "crate", "height": 1.5}], "rightLane": [{"x": 30, "y": 0, "z": 10, "type": "crate", "height": 1.5}, {"x": 25, "y": 0, "z": 5, "type": "barrel", "height": 1.0}, {"x": 20, "y": 0, "z": 0, "type": "wall", "height": 2.0}], "elevatedPositions": [{"x": -35, "y": 2, "z": 15, "type": "ramp", "height": 2.0}, {"x": 35, "y": 2, "z": 15, "type": "ramp", "height": 2.0}]}, "tacticalZones": {"chokePoints": [{"x": 0, "y": 0, "z": 0, "radius": 5, "importance": "high"}, {"x": -15, "y": 0, "z": 10, "radius": 3, "importance": "medium"}, {"x": 15, "y": 0, "z": 10, "radius": 3, "importance": "medium"}], "flankRoutes": [{"start": {"x": -40, "y": 0, "z": 30}, "end": {"x": 20, "y": 0, "z": -10}, "difficulty": "hard"}, {"start": {"x": 40, "y": 0, "z": 30}, "end": {"x": -20, "y": 0, "z": -10}, "difficulty": "hard"}], "sightLines": [{"from": {"x": -35, "y": 2, "z": 15}, "to": {"x": 35, "y": 0, "z": -15}, "type": "sniper"}, {"from": {"x": 0, "y": 0, "z": 10}, "to": {"x": 0, "y": 0, "z": -10}, "type": "assault"}]}, "rewardSystem": {"kill": 1.0, "assist": 0.3, "death": -0.5, "teamKill": -2.0, "objectiveComplete": 2.0, "coverUsage": 0.1, "teamwork": 0.2, "accuracy": 0.1, "survival": 0.05}}