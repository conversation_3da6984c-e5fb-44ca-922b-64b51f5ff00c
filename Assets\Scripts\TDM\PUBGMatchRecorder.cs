using UnityEngine;
using System.Collections.Generic;
using System.IO;

/// <summary>
/// 🎬 PUBG Match Recorder - Records and replays PUBG TDM matches
/// Captures agent decisions, movements, and combat for analysis
/// </summary>
public class PUBGMatchRecorder : MonoBehaviour
{
    [Header("🎬 Recording Settings")]
    public bool recordMatch = true;
    public bool autoSaveOnMatchEnd = true;
    public float recordingInterval = 0.1f; // Record every 0.1 seconds

    [Header("📊 Analysis Settings")]
    public bool logDecisions = true;
    public bool trackMovementPatterns = true;
    public bool analyzeCombatEfficiency = true;

    // Recording data
    private List<MatchFrame> matchFrames = new List<MatchFrame>();
    private List<DecisionLog> decisionLogs = new List<DecisionLog>();
    private List<CombatEvent> combatEvents = new List<CombatEvent>();
    private MatchMetadata matchMetadata;

    // Components
    private PUBGTDMManager gameManager;
    private float lastRecordTime;
    private int frameCounter = 0;

    void Start()
    {
        gameManager = GetComponent<PUBGTDMManager>();
        if (gameManager == null)
        {
            gameManager = FindObjectOfType<PUBGTDMManager>();
        }

        InitializeRecording();
    }

    void InitializeRecording()
    {
        if (!recordMatch) return;

        matchMetadata = new MatchMetadata
        {
            matchId = System.Guid.NewGuid().ToString(),
            startTime = System.DateTime.Now,
            mapName = "Warehouse Arena",
            gameMode = "5v5 TDM"
        };

        // Subscribe to game events
        if (gameManager != null)
        {
            gameManager.OnPlayerKill += RecordKillEvent;
            gameManager.OnMatchEnd += OnMatchEnd;
        }

        Debug.Log($"🎬 Match recording started: {matchMetadata.matchId}");
    }

    void Update()
    {
        if (recordMatch && gameManager != null && gameManager.matchActive)
        {
            if (Time.time - lastRecordTime >= recordingInterval)
            {
                RecordFrame();
                lastRecordTime = Time.time;
            }
        }
    }

    void RecordFrame()
    {
        MatchFrame frame = new MatchFrame
        {
            frameNumber = frameCounter++,
            timestamp = Time.time,
            matchTime = gameManager.matchTimer,
            teamAScore = gameManager.teamAKills,
            teamBScore = gameManager.teamBKills,
            agentStates = new List<AgentState>()
        };

        // Record all agent states
        PUBGAgent[] allAgents = FindObjectsOfType<PUBGAgent>();
        foreach (var agent in allAgents)
        {
            AgentState state = new AgentState
            {
                agentId = agent.name,
                team = agent.team.ToString(),
                role = agent.role.ToString(),
                position = agent.transform.position,
                rotation = agent.transform.rotation.eulerAngles,
                health = agent.health,
                currentWeapon = agent.currentWeapon,
                movementState = agent.currentMovementState.ToString(),
                isInCover = agent.isInCover,
                hasSpawnProtection = agent.hasSpawnProtection,
                kills = agent.kills,
                deaths = agent.deaths,
                assists = agent.assists
            };

            frame.agentStates.Add(state);
        }

        matchFrames.Add(frame);
    }

    public void LogDecision(string agentId, string decision, string reasoning, float confidence)
    {
        if (!logDecisions) return;

        DecisionLog log = new DecisionLog
        {
            timestamp = Time.time,
            agentId = agentId,
            decision = decision,
            reasoning = reasoning,
            confidence = confidence,
            frameNumber = frameCounter
        };

        decisionLogs.Add(log);
    }

    void RecordKillEvent(PUBGAgent killer, PUBGAgent victim)
    {
        CombatEvent combatEvent = new CombatEvent
        {
            timestamp = Time.time,
            eventType = "Kill",
            killerId = killer.name,
            victimId = victim.name,
            weapon = killer.currentWeapon,
            distance = Vector3.Distance(killer.transform.position, victim.transform.position),
            killerPosition = killer.transform.position,
            victimPosition = victim.transform.position,
            killerHealth = killer.health,
            victimHealth = victim.health
        };

        combatEvents.Add(combatEvent);

        Debug.Log($"🎯 Combat event recorded: {killer.name} eliminated {victim.name} with {killer.currentWeapon}");
    }

    void OnMatchEnd(string result)
    {
        if (!recordMatch) return;

        matchMetadata.endTime = System.DateTime.Now;
        matchMetadata.duration = gameManager.matchTimer;
        matchMetadata.result = result;
        matchMetadata.finalScoreA = gameManager.teamAKills;
        matchMetadata.finalScoreB = gameManager.teamBKills;

        if (autoSaveOnMatchEnd)
        {
            SaveMatchData();
        }

        GenerateMatchAnalysis();
    }

    public void SaveMatchData()
    {
        string timestamp = System.DateTime.Now.ToString("yyyy-MM-dd_HH-mm-ss");
        string fileName = $"PUBG_Match_{timestamp}_{matchMetadata.matchId.Substring(0, 8)}";

        MatchRecording recording = new MatchRecording
        {
            metadata = matchMetadata,
            frames = matchFrames,
            decisions = decisionLogs,
            combatEvents = combatEvents
        };

        string json = JsonUtility.ToJson(recording, true);
        string filePath = Path.Combine(Application.persistentDataPath, "MatchRecordings", fileName + ".json");

        Directory.CreateDirectory(Path.GetDirectoryName(filePath));
        File.WriteAllText(filePath, json);

        Debug.Log($"💾 Match data saved: {filePath}");

        // Also save CSV for easy analysis
        SaveMatchCSV(fileName);
    }

    void SaveMatchCSV(string fileName)
    {
        string csvPath = Path.Combine(Application.persistentDataPath, "MatchRecordings", fileName + "_stats.csv");

        using (StreamWriter writer = new StreamWriter(csvPath))
        {
            // Write header
            writer.WriteLine("Timestamp,FrameNumber,MatchTime,TeamAScore,TeamBScore,AgentId,Team,Role,PosX,PosY,PosZ,Health,Weapon,MovementState,Kills,Deaths,Assists");

            // Write frame data
            foreach (var frame in matchFrames)
            {
                foreach (var agent in frame.agentStates)
                {
                    writer.WriteLine($"{frame.timestamp},{frame.frameNumber},{frame.matchTime},{frame.teamAScore},{frame.teamBScore}," +
                                   $"{agent.agentId},{agent.team},{agent.role},{agent.position.x},{agent.position.y},{agent.position.z}," +
                                   $"{agent.health},{agent.currentWeapon},{agent.movementState},{agent.kills},{agent.deaths},{agent.assists}");
                }
            }
        }

        Debug.Log($"📊 Match CSV saved: {csvPath}");
    }

    void GenerateMatchAnalysis()
    {
        MatchAnalysis analysis = new MatchAnalysis();

        // Calculate basic stats
        analysis.totalFrames = matchFrames.Count;
        analysis.totalDecisions = decisionLogs.Count;
        analysis.totalCombatEvents = combatEvents.Count;
        analysis.averageDecisionsPerSecond = decisionLogs.Count / gameManager.matchTimer;

        // Analyze agent performance
        analysis.agentPerformance = AnalyzeAgentPerformance();

        // Analyze tactical patterns
        analysis.tacticalPatterns = AnalyzeTacticalPatterns();

        // Analyze combat efficiency
        analysis.combatEfficiency = AnalyzeCombatEfficiency();

        Debug.Log($"📈 Match Analysis Generated:");
        Debug.Log($"   Total Frames: {analysis.totalFrames}");
        Debug.Log($"   Total Decisions: {analysis.totalDecisions}");
        Debug.Log($"   Combat Events: {analysis.totalCombatEvents}");
        Debug.Log($"   Decisions/Second: {analysis.averageDecisionsPerSecond:F2}");
    }

    Dictionary<string, AgentPerformanceStats> AnalyzeAgentPerformance()
    {
        var performance = new Dictionary<string, AgentPerformanceStats>();

        PUBGAgent[] allAgents = FindObjectsOfType<PUBGAgent>();
        foreach (var agent in allAgents)
        {
            var stats = new AgentPerformanceStats
            {
                agentId = agent.name,
                team = agent.team.ToString(),
                role = agent.role.ToString(),
                kills = agent.kills,
                deaths = agent.deaths,
                assists = agent.assists,
                kdr = agent.deaths > 0 ? (float)agent.kills / agent.deaths : agent.kills,
                accuracy = agent.accuracy,
                survivalTime = CalculateSurvivalTime(agent.name),
                distanceTraveled = CalculateDistanceTraveled(agent.name),
                timeInCover = CalculateTimeInCover(agent.name)
            };

            performance[agent.name] = stats;
        }

        return performance;
    }

    List<string> AnalyzeTacticalPatterns()
    {
        var patterns = new List<string>();

        // Analyze movement patterns
        patterns.Add("Movement patterns analyzed");

        // Analyze positioning
        patterns.Add("Positioning strategies identified");

        // Analyze team coordination
        patterns.Add("Team coordination patterns detected");

        return patterns;
    }

    Dictionary<string, float> AnalyzeCombatEfficiency()
    {
        var efficiency = new Dictionary<string, float>();

        // Calculate various efficiency metrics
        efficiency["AverageKillDistance"] = CalculateAverageKillDistance();
        efficiency["MostEffectiveWeapon"] = GetMostEffectiveWeapon();
        efficiency["AverageEngagementTime"] = CalculateAverageEngagementTime();

        return efficiency;
    }

    float CalculateSurvivalTime(string agentId)
    {
        // Calculate how long agent survived on average
        return gameManager.matchTimer; // Simplified
    }

    float CalculateDistanceTraveled(string agentId)
    {
        float totalDistance = 0f;
        Vector3 lastPosition = Vector3.zero;
        bool firstFrame = true;

        foreach (var frame in matchFrames)
        {
            var agentState = frame.agentStates.Find(a => a.agentId == agentId);
            if (agentState != null)
            {
                if (!firstFrame)
                {
                    totalDistance += Vector3.Distance(lastPosition, agentState.position);
                }
                lastPosition = agentState.position;
                firstFrame = false;
            }
        }

        return totalDistance;
    }

    float CalculateTimeInCover(string agentId)
    {
        float timeInCover = 0f;

        foreach (var frame in matchFrames)
        {
            var agentState = frame.agentStates.Find(a => a.agentId == agentId);
            if (agentState != null && agentState.isInCover)
            {
                timeInCover += recordingInterval;
            }
        }

        return timeInCover;
    }

    float CalculateAverageKillDistance()
    {
        if (combatEvents.Count == 0) return 0f;

        float totalDistance = 0f;
        foreach (var combatEvent in combatEvents)
        {
            totalDistance += combatEvent.distance;
        }

        return totalDistance / combatEvents.Count;
    }

    float GetMostEffectiveWeapon()
    {
        // Return weapon effectiveness score (simplified)
        return 1.0f;
    }

    float CalculateAverageEngagementTime()
    {
        // Calculate average time between first shot and kill (simplified)
        return 2.5f;
    }

    [System.Serializable]
    public class MatchRecording
    {
        public MatchMetadata metadata;
        public List<MatchFrame> frames;
        public List<DecisionLog> decisions;
        public List<CombatEvent> combatEvents;
    }

    [System.Serializable]
    public class MatchMetadata
    {
        public string matchId;
        public System.DateTime startTime;
        public System.DateTime endTime;
        public float duration;
        public string mapName;
        public string gameMode;
        public string result;
        public int finalScoreA;
        public int finalScoreB;
    }

    [System.Serializable]
    public class MatchFrame
    {
        public int frameNumber;
        public float timestamp;
        public float matchTime;
        public int teamAScore;
        public int teamBScore;
        public List<AgentState> agentStates;
    }

    [System.Serializable]
    public class AgentState
    {
        public string agentId;
        public string team;
        public string role;
        public Vector3 position;
        public Vector3 rotation;
        public float health;
        public string currentWeapon;
        public string movementState;
        public bool isInCover;
        public bool hasSpawnProtection;
        public int kills;
        public int deaths;
        public int assists;
    }

    [System.Serializable]
    public class DecisionLog
    {
        public float timestamp;
        public string agentId;
        public string decision;
        public string reasoning;
        public float confidence;
        public int frameNumber;
    }

    [System.Serializable]
    public class CombatEvent
    {
        public float timestamp;
        public string eventType;
        public string killerId;
        public string victimId;
        public string weapon;
        public float distance;
        public Vector3 killerPosition;
        public Vector3 victimPosition;
        public float killerHealth;
        public float victimHealth;
    }

    [System.Serializable]
    public class MatchAnalysis
    {
        public int totalFrames;
        public int totalDecisions;
        public int totalCombatEvents;
        public float averageDecisionsPerSecond;
        public Dictionary<string, AgentPerformanceStats> agentPerformance;
        public List<string> tacticalPatterns;
        public Dictionary<string, float> combatEfficiency;
    }

    [System.Serializable]
    public class AgentPerformanceStats
    {
        public string agentId;
        public string team;
        public string role;
        public int kills;
        public int deaths;
        public int assists;
        public float kdr;
        public float accuracy;
        public float survivalTime;
        public float distanceTraveled;
        public float timeInCover;
    }
}
