using UnityEngine;
using UnityEditor;
using System.Collections.Generic;

/// <summary>
/// Generates PUBG-style TDM arena with proper layout matching the reference image
/// </summary>
public class PUBGArenaGenerator : EditorWindow
{
    [MenuItem("SquadMate AI/🗺️ Generate PUBG Arena")]
    public static void ShowWindow()
    {
        GetWindow<PUBGArenaGenerator>("PUBG Arena Generator");
    }

    void OnGUI()
    {
        GUILayout.Label("🗺️ PUBG-Style Arena Generator", EditorStyles.boldLabel);
        GUILayout.Space(10);

        GUILayout.Label("This will create a PUBG-style TDM arena with:");
        GUILayout.Label("• Central compound with multiple buildings");
        GUILayout.Label("• Outer perimeter buildings");
        GUILayout.Label("• Strategic cover positions");
        GUILayout.Label("• Proper spawn points and loot zones");
        GUILayout.Label("• NavMesh-ready geometry");

        GUILayout.Space(10);

        if (GUILayout.Button("🏗️ Generate PUBG Arena"))
        {
            GeneratePUBGArena();
        }

        if (GUILayout.Button("🧹 Clear Existing Arena"))
        {
            ClearExistingArena();
        }
    }

    void GeneratePUBGArena()
    {
        Debug.Log("🏗️ Generating PUBG-style TDM arena...");

        // Clear existing arena
        ClearExistingArena();

        // Create arena root
        GameObject arenaRoot = new GameObject("PUBG_TDM_Arena");
        arenaRoot.transform.position = Vector3.zero;

        // Create terrain base
        CreateTerrainBase(arenaRoot);

        // Create central compound
        CreateCentralCompound(arenaRoot);

        // Create outer buildings
        CreateOuterBuildings(arenaRoot);

        // Create cover objects
        CreateCoverObjects(arenaRoot);

        // Setup spawn points
        SetupSpawnPoints(arenaRoot);

        // Setup loot zones
        SetupLootZones(arenaRoot);

        // Add TDM Environment component
        TDMEnvironment tdmEnv = arenaRoot.AddComponent<TDMEnvironment>();
        ConfigureTDMEnvironment(tdmEnv, arenaRoot);

        Debug.Log("✅ PUBG arena generated successfully!");
        Selection.activeGameObject = arenaRoot;
    }

    void ClearExistingArena()
    {
        GameObject existing = GameObject.Find("PUBG_TDM_Arena") ?? GameObject.Find("Tdm") ?? GameObject.Find("TDM_Arena");
        if (existing != null)
        {
            DestroyImmediate(existing);
            Debug.Log("🧹 Cleared existing arena");
        }
    }

    void CreateTerrainBase(GameObject parent)
    {
        // Create main terrain plane (80x80 units)
        GameObject terrain = GameObject.CreatePrimitive(PrimitiveType.Plane);
        terrain.name = "Arena_Terrain";
        terrain.transform.parent = parent.transform;
        terrain.transform.localScale = new Vector3(8f, 1f, 8f); // 80x80 units
        terrain.transform.position = new Vector3(0, -0.5f, 0);

        // Set terrain material (dark gray)
        Material terrainMat = new Material(Shader.Find("Standard"));
        terrainMat.color = new Color(0.3f, 0.3f, 0.3f);
        terrain.GetComponent<Renderer>().material = terrainMat;

        // Mark as static for NavMesh
        terrain.isStatic = true;
    }

    void CreateCentralCompound(GameObject parent)
    {
        GameObject compound = new GameObject("Central_Compound");
        compound.transform.parent = parent.transform;

        // Main central building (large)
        CreateBuilding(compound, "Central_Main", Vector3.zero, new Vector3(12f, 4f, 8f));

        // Secondary buildings around center
        CreateBuilding(compound, "Central_North", new Vector3(0, 0, 15f), new Vector3(8f, 3f, 6f));
        CreateBuilding(compound, "Central_South", new Vector3(0, 0, -15f), new Vector3(8f, 3f, 6f));
        CreateBuilding(compound, "Central_East", new Vector3(15f, 0, 0), new Vector3(6f, 3f, 8f));
        CreateBuilding(compound, "Central_West", new Vector3(-15f, 0, 0), new Vector3(6f, 3f, 8f));

        // Small utility buildings
        CreateBuilding(compound, "Utility_NE", new Vector3(10f, 0, 10f), new Vector3(4f, 2.5f, 4f));
        CreateBuilding(compound, "Utility_NW", new Vector3(-10f, 0, 10f), new Vector3(4f, 2.5f, 4f));
        CreateBuilding(compound, "Utility_SE", new Vector3(10f, 0, -10f), new Vector3(4f, 2.5f, 4f));
        CreateBuilding(compound, "Utility_SW", new Vector3(-10f, 0, -10f), new Vector3(4f, 2.5f, 4f));
    }

    void CreateOuterBuildings(GameObject parent)
    {
        GameObject outerBuildings = new GameObject("Outer_Buildings");
        outerBuildings.transform.parent = parent.transform;

        // Perimeter buildings (matching PUBG layout)
        Vector3[] outerPositions = {
            new Vector3(30f, 0, 25f),   // NE outer
            new Vector3(-30f, 0, 25f),  // NW outer
            new Vector3(30f, 0, -25f),  // SE outer
            new Vector3(-30f, 0, -25f), // SW outer
            new Vector3(0f, 0, 35f),    // North outer
            new Vector3(0f, 0, -35f),   // South outer
            new Vector3(35f, 0, 0f),    // East outer
            new Vector3(-35f, 0, 0f),   // West outer
        };

        for (int i = 0; i < outerPositions.Length; i++)
        {
            Vector3 size = new Vector3(
                Random.Range(6f, 10f),
                Random.Range(2.5f, 4f),
                Random.Range(6f, 10f)
            );
            CreateBuilding(outerBuildings, $"Outer_Building_{i}", outerPositions[i], size);
        }
    }

    void CreateBuilding(GameObject parent, string name, Vector3 position, Vector3 size)
    {
        GameObject building = GameObject.CreatePrimitive(PrimitiveType.Cube);
        building.name = name;
        building.transform.parent = parent.transform;
        building.transform.position = position + new Vector3(0, size.y / 2f, 0);
        building.transform.localScale = size;

        // Set building material (light gray)
        Material buildingMat = new Material(Shader.Find("Standard"));
        buildingMat.color = new Color(0.8f, 0.8f, 0.8f);
        building.GetComponent<Renderer>().material = buildingMat;

        // Mark as static for NavMesh
        building.isStatic = true;

        // Add collider for cover
        building.GetComponent<BoxCollider>().isTrigger = false;
    }

    void CreateCoverObjects(GameObject parent)
    {
        GameObject coverObjects = new GameObject("Cover_Objects");
        coverObjects.transform.parent = parent.transform;

        // Create scattered cover objects (crates, barriers, etc.)
        Vector3[] coverPositions = {
            new Vector3(5f, 0, 20f), new Vector3(-5f, 0, 20f),
            new Vector3(20f, 0, 5f), new Vector3(20f, 0, -5f),
            new Vector3(-20f, 0, 5f), new Vector3(-20f, 0, -5f),
            new Vector3(5f, 0, -20f), new Vector3(-5f, 0, -20f),
        };

        for (int i = 0; i < coverPositions.Length; i++)
        {
            CreateCoverObject(coverObjects, $"Cover_{i}", coverPositions[i]);
        }
    }

    void CreateCoverObject(GameObject parent, string name, Vector3 position)
    {
        GameObject cover = GameObject.CreatePrimitive(PrimitiveType.Cube);
        cover.name = name;
        cover.transform.parent = parent.transform;
        cover.transform.position = position + new Vector3(0, 0.75f, 0);
        cover.transform.localScale = new Vector3(2f, 1.5f, 2f);

        // Set cover material (brown)
        Material coverMat = new Material(Shader.Find("Standard"));
        coverMat.color = new Color(0.6f, 0.4f, 0.2f);
        cover.GetComponent<Renderer>().material = coverMat;

        // Mark as static for NavMesh
        cover.isStatic = true;
    }

    void SetupSpawnPoints(GameObject parent)
    {
        GameObject spawnPoints = new GameObject("Spawn_Points");
        spawnPoints.transform.parent = parent.transform;

        // Team A spawn points (North side)
        CreateTeamSpawns(spawnPoints, "TeamA", new Vector3(0, 0, 30f), 5);

        // Team B spawn points (South side)
        CreateTeamSpawns(spawnPoints, "TeamB", new Vector3(0, 0, -30f), 5);
    }

    void CreateTeamSpawns(GameObject parent, string teamName, Vector3 basePosition, int count)
    {
        GameObject teamSpawns = new GameObject($"{teamName}_Spawns");
        teamSpawns.transform.parent = parent.transform;

        for (int i = 0; i < count; i++)
        {
            GameObject spawn = new GameObject($"{teamName}_Spawn_{i}");
            spawn.transform.parent = teamSpawns.transform;

            Vector3 offset = new Vector3((i - 2) * 3f, 0, 0); // Spread spawns horizontally
            spawn.transform.position = basePosition + offset;

            // Add spawn point component
            spawn.AddComponent<SpawnPoint>();
        }
    }

    void SetupLootZones(GameObject parent)
    {
        GameObject lootZones = new GameObject("Loot_Zones");
        lootZones.transform.parent = parent.transform;

        // Create weapon spawn points
        CreateLootSpawns(lootZones, "Weapon_Spawns", GetWeaponSpawnPositions());

        // Create health spawn points
        CreateLootSpawns(lootZones, "Health_Spawns", GetHealthSpawnPositions());

        // Create armor spawn points
        CreateLootSpawns(lootZones, "Armor_Spawns", GetArmorSpawnPositions());
    }

    void CreateLootSpawns(GameObject parent, string spawnType, Vector3[] positions)
    {
        GameObject spawnGroup = new GameObject(spawnType);
        spawnGroup.transform.parent = parent.transform;

        for (int i = 0; i < positions.Length; i++)
        {
            GameObject spawn = new GameObject($"{spawnType}_{i}");
            spawn.transform.parent = spawnGroup.transform;
            spawn.transform.position = positions[i];
        }
    }

    Vector3[] GetWeaponSpawnPositions()
    {
        return new Vector3[] {
            new Vector3(0, 0, 0),      // Center
            new Vector3(15f, 0, 15f),  // NE building
            new Vector3(-15f, 0, 15f), // NW building
            new Vector3(15f, 0, -15f), // SE building
            new Vector3(-15f, 0, -15f),// SW building
            new Vector3(30f, 0, 0),    // East outer
            new Vector3(-30f, 0, 0),   // West outer
        };
    }

    Vector3[] GetHealthSpawnPositions()
    {
        return new Vector3[] {
            new Vector3(10f, 0, 10f),
            new Vector3(-10f, 0, 10f),
            new Vector3(10f, 0, -10f),
            new Vector3(-10f, 0, -10f),
            new Vector3(25f, 0, 25f),
            new Vector3(-25f, 0, -25f),
        };
    }

    Vector3[] GetArmorSpawnPositions()
    {
        return new Vector3[] {
            new Vector3(0, 0, 15f),    // North building
            new Vector3(0, 0, -15f),   // South building
            new Vector3(30f, 0, 25f),  // NE outer
            new Vector3(-30f, 0, -25f),// SW outer
        };
    }

    void ConfigureTDMEnvironment(TDMEnvironment tdmEnv, GameObject arenaRoot)
    {
        // Find and assign spawn points
        Transform weaponSpawns = arenaRoot.transform.Find("Loot_Zones/Weapon_Spawns");
        Transform healthSpawns = arenaRoot.transform.Find("Loot_Zones/Health_Spawns");
        Transform armorSpawns = arenaRoot.transform.Find("Loot_Zones/Armor_Spawns");

        // Use SerializedObject to assign arrays properly
        SerializedObject serializedTDM = new SerializedObject(tdmEnv);

        if (weaponSpawns != null)
        {
            SerializedProperty weaponSpawnsProp = serializedTDM.FindProperty("weaponSpawnPoints");
            weaponSpawnsProp.arraySize = weaponSpawns.childCount;
            for (int i = 0; i < weaponSpawns.childCount; i++)
            {
                weaponSpawnsProp.GetArrayElementAtIndex(i).objectReferenceValue = weaponSpawns.GetChild(i);
            }
            Debug.Log($"✅ Assigned {weaponSpawns.childCount} weapon spawn points");
        }

        if (healthSpawns != null)
        {
            SerializedProperty healthSpawnsProp = serializedTDM.FindProperty("healthSpawnPoints");
            healthSpawnsProp.arraySize = healthSpawns.childCount;
            for (int i = 0; i < healthSpawns.childCount; i++)
            {
                healthSpawnsProp.GetArrayElementAtIndex(i).objectReferenceValue = healthSpawns.GetChild(i);
            }
            Debug.Log($"✅ Assigned {healthSpawns.childCount} health spawn points");
        }

        if (armorSpawns != null)
        {
            SerializedProperty armorSpawnsProp = serializedTDM.FindProperty("armorSpawnPoints");
            armorSpawnsProp.arraySize = armorSpawns.childCount;
            for (int i = 0; i < armorSpawns.childCount; i++)
            {
                armorSpawnsProp.GetArrayElementAtIndex(i).objectReferenceValue = armorSpawns.GetChild(i);
            }
            Debug.Log($"✅ Assigned {armorSpawns.childCount} armor spawn points");
        }

        serializedTDM.ApplyModifiedProperties();
        tdmEnv.arenaRoot = arenaRoot.transform;
        Debug.Log("✅ TDMEnvironment configured with all spawn points");
    }
}

/// <summary>
/// Simple spawn point marker component
/// </summary>
public class SpawnPoint : MonoBehaviour
{
    public int teamID = 0;

    void OnDrawGizmos()
    {
        Gizmos.color = teamID == 0 ? Color.blue : Color.red;
        Gizmos.DrawWireSphere(transform.position, 1f);
        Gizmos.DrawLine(transform.position, transform.position + Vector3.up * 2f);
    }
}
