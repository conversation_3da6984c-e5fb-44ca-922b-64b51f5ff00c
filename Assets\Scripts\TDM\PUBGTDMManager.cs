using UnityEngine;
using System.Collections.Generic;
using System.Collections;
using Newtonsoft.Json;

// PUBG TDM Data Structures
[System.Serializable]
public class PUBGTDMConfig
{
    public GameModeConfig gameMode;
    public MapConfig map;
    public WeaponsConfig weapons;
    public PlayerStatsConfig playerStats;
    public DamageMultipliersConfig damageMultipliers;
    public Dictionary<string, AIRoleConfig> aiRoles;
    public CoverPointsConfig coverPoints;
    public TacticalZonesConfig tacticalZones;
    public RewardSystemConfig rewardSystem;
}

[System.Serializable]
public class GameModeConfig
{
    public string name;
    public string objective;
    public int maxKills;
    public float timeLimit;
    public int teamSize;
    public float respawnDelay;
    public float spawnProtectionTime;
}

[System.Serializable]
public class MapConfig
{
    public string name;
    public string type;
    public MapSize size;
    public string[] lanes;
    public SpawnZonesConfig spawnZones;
}

[System.Serializable]
public class MapSize
{
    public float width;
    public float length;
    public float height;
}

[System.Serializable]
public class SpawnZonesConfig
{
    public TeamSpawnConfig teamA;
    public TeamSpawnConfig teamB;
}

[System.Serializable]
public class TeamSpawnConfig
{
    public Position[] positions;
    public SafeZone safeZone;
}

[System.Serializable]
public class Position
{
    public float x, y, z;
}

[System.Serializable]
public class SafeZone
{
    public float x, y, z, radius;
}

[System.Serializable]
public class WeaponsConfig
{
    public Dictionary<string, WeaponData> assaultRifles;
    public Dictionary<string, WeaponData> submachineGuns;
    public Dictionary<string, WeaponData> sniperRifles;
}

[System.Serializable]
public class WeaponData
{
    public float damage;
    public float fireRate;
    public float range;
    public float accuracy;
    public float recoil;
    public string ammoType;
    public int magazineSize;
    public float reloadTime;
}

[System.Serializable]
public class PlayerStatsConfig
{
    public float health;
    public float maxHealth;
    public float movementSpeed;
    public float sprintSpeed;
    public float crouchSpeed;
    public float proneSpeed;
    public float jumpHeight;
    public float slideDistance;
}

[System.Serializable]
public class DamageMultipliersConfig
{
    public float headshot;
    public float chest;
    public float limbs;
}

[System.Serializable]
public class AIRoleConfig
{
    public string description;
    public string[] preferredWeapons;
    public BehaviorConfig behavior;
    public string[] tactics;
}

[System.Serializable]
public class BehaviorConfig
{
    public float aggression;
    public float teamwork;
    public string positioning;
    public string engagementRange;
}

[System.Serializable]
public class CoverPointsConfig
{
    public CoverPoint[] leftLane;
    public CoverPoint[] centerLane;
    public CoverPoint[] rightLane;
    public CoverPoint[] elevatedPositions;
}

[System.Serializable]
public class CoverPoint
{
    public float x, y, z;
    public string type;
    public float height;
}

[System.Serializable]
public class TacticalZonesConfig
{
    public ChokePoint[] chokePoints;
    public FlankRoute[] flankRoutes;
    public SightLine[] sightLines;
}

[System.Serializable]
public class ChokePoint
{
    public float x, y, z, radius;
    public string importance;
}

[System.Serializable]
public class FlankRoute
{
    public Position start;
    public Position end;
    public string difficulty;
}

[System.Serializable]
public class SightLine
{
    public Position from;
    public Position to;
    public string type;
}

[System.Serializable]
public class RewardSystemConfig
{
    public float kill;
    public float assist;
    public float death;
    public float teamKill;
    public float objectiveComplete;
    public float coverUsage;
    public float teamwork;
    public float accuracy;
    public float survival;
}

// Enums
public enum PUBGTeam { TeamA, TeamB }
public enum PUBGRole { Assault, Support, Sniper, Scout, Anchor }
public enum MovementState { Walking, Running, Crouching, Prone, Sliding, Jumping }

/// <summary>
/// PUBG Mobile 5v5 TDM Manager - Handles authentic PUBG TDM gameplay mechanics
/// Based on real PUBG Mobile Arena Warehouse gameplay
/// </summary>
public class PUBGTDMManager : MonoBehaviour
{
    [Header("🎮 PUBG TDM Configuration")]
    public TextAsset configFile;
    public bool autoLoadConfig = true;

    [Header("🏟️ Match Settings")]
    public int maxKills = 40;
    public float matchDuration = 600f; // 10 minutes
    public float respawnDelay = 3f;
    public float spawnProtectionTime = 3f;

    [Header("👥 Teams")]
    public List<PUBGAgent> teamA = new List<PUBGAgent>();
    public List<PUBGAgent> teamB = new List<PUBGAgent>();

    [Header("📊 Match Stats")]
    public int teamAKills = 0;
    public int teamBKills = 0;
    public float matchTimer = 0f;
    public bool matchActive = false;

    [Header("🗺️ Map Elements")]
    public Transform[] teamASpawns;
    public Transform[] teamBSpawns;
    public Transform[] coverPoints;
    public Transform[] tacticalZones;

    // Configuration data
    private PUBGTDMConfig config;
    private Dictionary<string, WeaponData> weaponDatabase;
    private List<Vector3> teamASpawnPositions;
    private List<Vector3> teamBSpawnPositions;

    // Events
    public System.Action<int, int> OnScoreUpdate;
    public System.Action<string> OnMatchEnd;
    public System.Action<PUBGAgent, PUBGAgent> OnPlayerKill;

    void Start()
    {
        if (autoLoadConfig)
        {
            LoadConfiguration();
        }
        InitializeMatch();
    }

    void LoadConfiguration()
    {
        if (configFile != null)
        {
            try
            {
                config = JsonConvert.DeserializeObject<PUBGTDMConfig>(configFile.text);
                ApplyConfiguration();
                Debug.Log("✅ PUBG TDM Configuration loaded successfully");
            }
            catch (System.Exception e)
            {
                Debug.LogError($"❌ Failed to load PUBG TDM config: {e.Message}");
            }
        }
    }

    void ApplyConfiguration()
    {
        if (config == null) return;

        maxKills = config.gameMode.maxKills;
        matchDuration = config.gameMode.timeLimit;
        respawnDelay = config.gameMode.respawnDelay;
        spawnProtectionTime = config.gameMode.spawnProtectionTime;

        // Build weapon database
        weaponDatabase = new Dictionary<string, WeaponData>();

        // Add assault rifles
        foreach (var weapon in config.weapons.assaultRifles)
        {
            weaponDatabase[weapon.Key] = weapon.Value;
        }

        // Add SMGs
        foreach (var weapon in config.weapons.submachineGuns)
        {
            weaponDatabase[weapon.Key] = weapon.Value;
        }

        // Add sniper rifles
        foreach (var weapon in config.weapons.sniperRifles)
        {
            weaponDatabase[weapon.Key] = weapon.Value;
        }

        Debug.Log($"🔫 Loaded {weaponDatabase.Count} weapons from config");
    }

    void InitializeMatch()
    {
        Debug.Log("🚀 Initializing PUBG 5v5 TDM Match...");

        SetupSpawnPositions();
        SpawnTeams();
        StartMatch();
    }

    void SetupSpawnPositions()
    {
        teamASpawnPositions = new List<Vector3>();
        teamBSpawnPositions = new List<Vector3>();

        if (config != null)
        {
            // Use config positions
            foreach (var pos in config.map.spawnZones.teamA.positions)
            {
                teamASpawnPositions.Add(new Vector3(pos.x, pos.y, pos.z));
            }

            foreach (var pos in config.map.spawnZones.teamB.positions)
            {
                teamBSpawnPositions.Add(new Vector3(pos.x, pos.y, pos.z));
            }
        }
        else
        {
            // Fallback to transform positions
            foreach (var spawn in teamASpawns)
            {
                teamASpawnPositions.Add(spawn.position);
            }

            foreach (var spawn in teamBSpawns)
            {
                teamBSpawnPositions.Add(spawn.position);
            }
        }

        Debug.Log($"📍 Setup {teamASpawnPositions.Count} Team A spawns, {teamBSpawnPositions.Count} Team B spawns");
    }

    void SpawnTeams()
    {
        // Clear existing teams
        teamA.Clear();
        teamB.Clear();

        // Spawn Team A
        for (int i = 0; i < 5; i++)
        {
            Vector3 spawnPos = teamASpawnPositions[i % teamASpawnPositions.Count];
            PUBGAgent agent = CreatePUBGAgent($"TeamA_Player_{i}", spawnPos, PUBGTeam.TeamA, GetRoleForIndex(i));
            teamA.Add(agent);
        }

        // Spawn Team B
        for (int i = 0; i < 5; i++)
        {
            Vector3 spawnPos = teamBSpawnPositions[i % teamBSpawnPositions.Count];
            PUBGAgent agent = CreatePUBGAgent($"TeamB_Player_{i}", spawnPos, PUBGTeam.TeamB, GetRoleForIndex(i));
            teamB.Add(agent);
        }

        Debug.Log($"👥 Spawned teams: {teamA.Count} vs {teamB.Count}");
    }

    PUBGAgent CreatePUBGAgent(string agentName, Vector3 position, PUBGTeam team, PUBGRole role)
    {
        // Create agent GameObject
        GameObject agentObj = new GameObject(agentName);
        agentObj.transform.position = position;

        // Add required components
        PUBGAgent agent = agentObj.AddComponent<PUBGAgent>();
        agentObj.AddComponent<UnityEngine.AI.NavMeshAgent>();
        agentObj.AddComponent<Rigidbody>();
        agentObj.AddComponent<CapsuleCollider>();

        // Configure agent
        agent.Initialize(team, role, this);
        agent.AssignWeapon(GetWeaponForRole(role));

        // Set team colors
        Renderer renderer = agentObj.GetComponent<Renderer>();
        if (renderer == null)
        {
            renderer = agentObj.AddComponent<MeshRenderer>();
            agentObj.AddComponent<MeshFilter>().mesh = CreateAgentMesh();
        }

        renderer.material = new Material(Shader.Find("Standard"));
        renderer.material.color = team == PUBGTeam.TeamA ? Color.blue : Color.red;

        return agent;
    }

    PUBGRole GetRoleForIndex(int index)
    {
        PUBGRole[] roles = { PUBGRole.Assault, PUBGRole.Support, PUBGRole.Sniper, PUBGRole.Scout, PUBGRole.Anchor };
        return roles[index % roles.Length];
    }

    string GetWeaponForRole(PUBGRole role)
    {
        switch (role)
        {
            case PUBGRole.Assault: return "M416";
            case PUBGRole.Support: return "SCAR-L";
            case PUBGRole.Sniper: return "Kar98k";
            case PUBGRole.Scout: return "Vector";
            case PUBGRole.Anchor: return "AKM";
            default: return "M416";
        }
    }

    Mesh CreateAgentMesh()
    {
        // Create a simple capsule mesh for the agent
        GameObject capsule = GameObject.CreatePrimitive(PrimitiveType.Capsule);
        Mesh mesh = capsule.GetComponent<MeshFilter>().mesh;
        DestroyImmediate(capsule);
        return mesh;
    }

    public void StartMatch()
    {
        matchActive = true;
        matchTimer = 0f;
        teamAKills = 0;
        teamBKills = 0;

        Debug.Log("🚀 PUBG 5v5 TDM Match Started!");
        Debug.Log($"🎯 First to {maxKills} kills or highest score in {matchDuration / 60f:F1} minutes wins!");

        StartCoroutine(MatchTimer());
        OnScoreUpdate?.Invoke(teamAKills, teamBKills);
    }

    IEnumerator MatchTimer()
    {
        while (matchActive && matchTimer < matchDuration)
        {
            matchTimer += Time.deltaTime;
            yield return null;
        }

        if (matchActive)
        {
            EndMatch("Time Limit Reached");
        }
    }

    public void RegisterKill(PUBGAgent killer, PUBGAgent victim)
    {
        if (!matchActive) return;

        if (killer.team == PUBGTeam.TeamA)
        {
            teamAKills++;
        }
        else
        {
            teamBKills++;
        }

        Debug.Log($"💀 {killer.name} eliminated {victim.name}! Score: {teamAKills} - {teamBKills}");

        OnPlayerKill?.Invoke(killer, victim);
        OnScoreUpdate?.Invoke(teamAKills, teamBKills);

        // Check win condition
        if (teamAKills >= maxKills)
        {
            EndMatch("Team A Wins!");
        }
        else if (teamBKills >= maxKills)
        {
            EndMatch("Team B Wins!");
        }

        // Respawn victim after delay
        StartCoroutine(RespawnPlayer(victim));
    }

    IEnumerator RespawnPlayer(PUBGAgent player)
    {
        yield return new WaitForSeconds(respawnDelay);

        if (matchActive)
        {
            Vector3 spawnPos = GetRespawnPosition(player.team);
            player.Respawn(spawnPos);

            // Apply spawn protection
            StartCoroutine(ApplySpawnProtection(player));
        }
    }

    IEnumerator ApplySpawnProtection(PUBGAgent player)
    {
        player.SetSpawnProtection(true);
        yield return new WaitForSeconds(spawnProtectionTime);
        player.SetSpawnProtection(false);
    }

    Vector3 GetRespawnPosition(PUBGTeam team)
    {
        List<Vector3> spawnPositions = team == PUBGTeam.TeamA ? teamASpawnPositions : teamBSpawnPositions;
        return spawnPositions[Random.Range(0, spawnPositions.Count)];
    }

    void EndMatch(string reason)
    {
        matchActive = false;

        string winner = teamAKills > teamBKills ? "Team A" :
                       teamBKills > teamAKills ? "Team B" : "Draw";

        Debug.Log($"🏁 Match Ended: {reason}");
        Debug.Log($"🏆 Final Score: Team A {teamAKills} - {teamBKills} Team B");
        Debug.Log($"🥇 Winner: {winner}");

        OnMatchEnd?.Invoke($"{reason} - {winner}");
    }

    public WeaponData GetWeaponData(string weaponName)
    {
        return weaponDatabase.ContainsKey(weaponName) ? weaponDatabase[weaponName] : null;
    }

    public PUBGTDMConfig GetConfig()
    {
        return config;
    }

    void Update()
    {
        if (matchActive)
        {
            // Update match timer display or other real-time elements
        }
    }
}
