using UnityEngine;

/// <summary>
/// 💡 Hover Tooltip Showing Role + Gear
/// Displays detailed agent information when hovering over agents in the scene
/// </summary>
public class AgentTooltip : MonoBehaviour
{
    [Header("🖱️ Tooltip Settings")]
    public GameObject tooltipPrefab;
    public float tooltipOffset = 2f;
    public float tooltipDelay = 0.5f;
    public bool followMouse = true;
    public bool showDetailedStats = true;

    [Header("🎨 Visual Settings")]
    public Color teamAColor = Color.blue;
    public Color teamBColor = Color.red;
    public Color neutralColor = Color.white;

    private GameObject tooltipInstance;
    private Camera mainCamera;
    private bool isHovering = false;
    private float hoverStartTime;

    // Agent components
    private VictorAgent victorAgent;
    private SquadMateAgent squadMateAgent;
    private AgentStats agentStats;
    private HealthSystem healthSystem;
    private InventorySystem inventorySystem;

    void Start()
    {
        mainCamera = Camera.main;
        if (mainCamera == null)
        {
            mainCamera = FindObjectOfType<Camera>();
        }

        // Get agent components
        victorAgent = GetComponent<VictorAgent>();
        squadMateAgent = GetComponent<SquadMateAgent>();
        agentStats = GetComponent<AgentStats>();
        healthSystem = GetComponent<HealthSystem>();
        inventorySystem = GetComponent<InventorySystem>();

        // Create tooltip prefab if not assigned
        if (tooltipPrefab == null)
        {
            CreateDefaultTooltipPrefab();
        }

        // Add collider if missing (needed for mouse detection)
        if (GetComponent<Collider>() == null)
        {
            CapsuleCollider capsule = gameObject.AddComponent<CapsuleCollider>();
            capsule.height = 2f;
            capsule.radius = 0.5f;
            capsule.isTrigger = true;
        }
    }

    void CreateDefaultTooltipPrefab()
    {
        // Create a simple tooltip prefab using basic Unity components
        GameObject tooltip = new GameObject("AgentTooltip");

        // Add a simple quad for background
        GameObject background = GameObject.CreatePrimitive(PrimitiveType.Quad);
        background.name = "Background";
        background.transform.SetParent(tooltip.transform);
        background.transform.localPosition = Vector3.zero;
        background.transform.localScale = new Vector3(3f, 2f, 1f);

        // Remove collider from background
        Collider bgCollider = background.GetComponent<Collider>();
        if (bgCollider != null) DestroyImmediate(bgCollider);

        // Set background material
        Renderer bgRenderer = background.GetComponent<Renderer>();
        if (bgRenderer != null)
        {
            bgRenderer.material = new Material(Shader.Find("Sprites/Default"));
            bgRenderer.material.color = new Color(0f, 0f, 0f, 0.8f);
        }

        // Add text using TextMesh (built-in Unity component)
        GameObject textObj = new GameObject("Text");
        textObj.transform.SetParent(tooltip.transform);
        textObj.transform.localPosition = Vector3.zero;

        TextMesh textMesh = textObj.AddComponent<TextMesh>();
        textMesh.text = "Agent Info";
        textMesh.fontSize = 20;
        textMesh.color = Color.white;
        textMesh.anchor = TextAnchor.MiddleCenter;
        textMesh.alignment = TextAlignment.Center;

        // Add MeshRenderer for text
        MeshRenderer textRenderer = textObj.GetComponent<MeshRenderer>();
        if (textRenderer != null)
        {
            textRenderer.material = new Material(Shader.Find("GUI/Text Shader"));
        }

        tooltipPrefab = tooltip;

        // Make it a prefab for reuse
        tooltip.SetActive(false);
    }

    void OnMouseEnter()
    {
        if (!isHovering)
        {
            isHovering = true;
            hoverStartTime = Time.time;

            if (tooltipDelay <= 0f)
            {
                ShowTooltip();
            }
            else
            {
                Invoke(nameof(ShowTooltip), tooltipDelay);
            }
        }
    }

    void OnMouseExit()
    {
        isHovering = false;
        CancelInvoke(nameof(ShowTooltip));
        HideTooltip();
    }

    void Update()
    {
        if (tooltipInstance != null && followMouse && mainCamera != null)
        {
            // Position tooltip near mouse cursor
            Vector3 mousePos = Input.mousePosition;
            Vector3 worldPos = mainCamera.ScreenToWorldPoint(new Vector3(mousePos.x, mousePos.y, mainCamera.nearClipPlane + 5f));
            tooltipInstance.transform.position = worldPos;
        }
    }

    void ShowTooltip()
    {
        if (!isHovering || tooltipInstance != null) return;

        Vector3 tooltipPosition = transform.position + Vector3.up * tooltipOffset;

        if (followMouse && mainCamera != null)
        {
            Vector3 mousePos = Input.mousePosition;
            tooltipPosition = mainCamera.ScreenToWorldPoint(new Vector3(mousePos.x, mousePos.y, mainCamera.nearClipPlane + 5f));
        }

        tooltipInstance = Instantiate(tooltipPrefab, tooltipPosition, Quaternion.LookRotation(mainCamera.transform.forward));
        tooltipInstance.SetActive(true);

        // Update tooltip content
        UpdateTooltipContent();
    }

    void HideTooltip()
    {
        if (tooltipInstance != null)
        {
            Destroy(tooltipInstance);
            tooltipInstance = null;
        }
    }

    void UpdateTooltipContent()
    {
        if (tooltipInstance == null) return;

        TextMesh tooltipText = tooltipInstance.GetComponentInChildren<TextMesh>();
        if (tooltipText == null) return;

        string content = BuildTooltipContent();
        tooltipText.text = content;

        // Update background color based on team
        Renderer backgroundRenderer = tooltipInstance.GetComponentInChildren<Renderer>();
        if (backgroundRenderer != null)
        {
            Color teamColor = GetTeamColor();
            backgroundRenderer.material.color = new Color(teamColor.r, teamColor.g, teamColor.b, 0.8f);
        }
    }

    string BuildTooltipContent()
    {
        System.Text.StringBuilder sb = new System.Text.StringBuilder();

        // Agent name and basic info (simplified for TextMesh)
        sb.AppendLine(gameObject.name);
        sb.AppendLine();

        // Role information
        if (victorAgent != null)
        {
            AgentRoleComponent roleComponent = victorAgent.GetComponent<AgentRoleComponent>();
            string role = roleComponent?.currentRole ?? "Unknown";
            sb.AppendLine($"Role: {role}");
            sb.AppendLine($"Team: {victorAgent.teamID}");
        }
        else if (squadMateAgent != null)
        {
            sb.AppendLine("Role: SquadMate");
            sb.AppendLine($"Team: {(gameObject.tag.Contains("TeamA") ? "Team A" : "Team B")}");
        }

        // Health information
        if (healthSystem != null)
        {
            float healthPercent = (healthSystem.currentHealth / healthSystem.maxHealth) * 100f;
            sb.AppendLine($"Health: {healthSystem.currentHealth:F0}/{healthSystem.maxHealth:F0} ({healthPercent:F0}%)");
        }

        // Weapon information
        if (inventorySystem != null)
        {
            string weapon = string.IsNullOrEmpty(inventorySystem.equippedWeapon) ? "None" : inventorySystem.equippedWeapon;
            sb.AppendLine($"Weapon: {weapon}");

            int healingCount = inventorySystem.GetHealingCount();
            if (healingCount > 0)
            {
                sb.AppendLine($"Healing Items: {healingCount}");
            }
        }

        // Detailed stats (if enabled) - simplified for TextMesh
        if (showDetailedStats && agentStats != null)
        {
            sb.AppendLine();
            sb.AppendLine("Combat Stats:");
            sb.AppendLine($"Kills: {agentStats.kills}");
            sb.AppendLine($"Deaths: {agentStats.deaths}");
            sb.AppendLine($"K/D: {agentStats.GetKDR():F2}");
            sb.AppendLine($"Revives: {agentStats.revives}");
            sb.AppendLine($"Zones: {agentStats.zonesHeld}");
        }

        // Current status
        sb.AppendLine();
        sb.AppendLine($"Status: {GetAgentStatus()}");

        return sb.ToString();
    }

    Color GetTeamColor()
    {
        if (victorAgent != null)
        {
            return victorAgent.teamID == TeamID.TeamA ? teamAColor : teamBColor;
        }
        else if (squadMateAgent != null)
        {
            return gameObject.tag.Contains("TeamA") ? teamAColor : teamBColor;
        }
        return neutralColor;
    }

    string GetAgentStatus()
    {
        if (healthSystem != null && healthSystem.currentHealth <= 0)
        {
            return "Eliminated";
        }

        if (victorAgent != null)
        {
            // Add more specific status based on agent behavior
            if (victorAgent.isInCombat)
            {
                return "In Combat";
            }
            else if (inventorySystem != null && inventorySystem.hasMedkit && healthSystem != null && healthSystem.currentHealth < healthSystem.maxHealth * 0.5f)
            {
                return "Healing";
            }
            else
            {
                return "Patrolling";
            }
        }

        return "Active";
    }

    void OnDestroy()
    {
        HideTooltip();
    }

    // Public method to manually show tooltip (for UI buttons, etc.)
    public void ShowTooltipManual()
    {
        isHovering = true;
        ShowTooltip();
    }

    // Public method to manually hide tooltip
    public void HideTooltipManual()
    {
        isHovering = false;
        HideTooltip();
    }
}
