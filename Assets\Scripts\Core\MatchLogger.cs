using UnityEngine;
using System.IO;
using System.Text;
using System.Collections.Generic;
using System;

/// <summary>
/// 🔁 Auto-Export Match Stats to CSV/JSON
/// Logs detailed match statistics for analysis and training evaluation
/// </summary>
public class MatchLogger : MonoBehaviour
{
    [Header("📊 Export Settings")]
    public string csvFileName = "match_results.csv";
    public string jsonFileName = "match_results.json";
    public bool exportToCSV = true;
    public bool exportToJSON = true;
    public bool autoExportOnMatchEnd = true;

    [Header("📁 File Paths")]
    public string customOutputPath = ""; // Leave empty to use persistent data path

    private string csvPath;
    private string jsonPath;
    private List<MatchResult> matchHistory = new List<MatchResult>();

    [System.Serializable]
    public class MatchResult
    {
        public string timestamp;
        public string agentName;
        public string teamID;
        public string role;
        public int kills;
        public int deaths;
        public float kdr;
        public int revives;
        public int zonesHeld;
        public float survivalTime;
        public int damageDealt;
        public int damageTaken;
        public string weaponUsed;
        public bool matchWinner;
        public float finalScore;
    }

    void Start()
    {
        InitializePaths();
        CreateCSVHeader();

        // Subscribe to match events
        if (FindObjectOfType<SquadManager>() != null)
        {
            Debug.Log("📊 MatchLogger initialized and connected to SquadManager");
        }
    }

    void InitializePaths()
    {
        string basePath = string.IsNullOrEmpty(customOutputPath) ?
            Application.persistentDataPath : customOutputPath;

        csvPath = Path.Combine(basePath, csvFileName);
        jsonPath = Path.Combine(basePath, jsonFileName);

        Debug.Log($"📁 Match logs will be saved to: {basePath}");
    }

    void CreateCSVHeader()
    {
        if (exportToCSV && !File.Exists(csvPath))
        {
            string header = "Timestamp,AgentName,TeamID,Role,Kills,Deaths,KDR,Revives,ZonesHeld,SurvivalTime,DamageDealt,DamageTaken,WeaponUsed,MatchWinner,FinalScore\n";
            File.WriteAllText(csvPath, header);
            Debug.Log("📄 CSV header created");
        }
    }

    public void LogMatchResults()
    {
        Debug.Log("📊 Collecting match statistics...");

        // Find all agents in the scene
        AgentStats[] allStats = FindObjectsOfType<AgentStats>();
        VictorAgent[] victorAgents = FindObjectsOfType<VictorAgent>();
        SquadMateAgent[] squadAgents = FindObjectsOfType<SquadMateAgent>();

        List<MatchResult> currentMatchResults = new List<MatchResult>();

        // Log VictorAgent stats
        foreach (var agent in victorAgents)
        {
            MatchResult result = CreateMatchResult(agent);
            currentMatchResults.Add(result);
            matchHistory.Add(result);
        }

        // Log SquadMateAgent stats (fallback)
        foreach (var agent in squadAgents)
        {
            if (System.Array.Find(victorAgents, va => va.gameObject == agent.gameObject) == null)
            {
                MatchResult result = CreateMatchResultFromSquadMate(agent);
                currentMatchResults.Add(result);
                matchHistory.Add(result);
            }
        }

        // Export results
        if (exportToCSV) ExportToCSV(currentMatchResults);
        if (exportToJSON) ExportToJSON(currentMatchResults);

        Debug.Log($"✅ Match results exported: {currentMatchResults.Count} agents logged");
    }

    MatchResult CreateMatchResult(VictorAgent agent)
    {
        AgentStats stats = agent.GetComponent<AgentStats>();
        HealthSystem health = agent.GetComponent<HealthSystem>();
        InventorySystem inventory = agent.GetComponent<InventorySystem>();
        AgentRoleComponent roleComponent = agent.GetComponent<AgentRoleComponent>();

        return new MatchResult
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            agentName = agent.name,
            teamID = agent.teamID.ToString(),
            role = roleComponent?.currentRole ?? "Unknown",
            kills = stats?.kills ?? 0,
            deaths = stats?.deaths ?? 0,
            kdr = stats?.GetKDR() ?? 0f,
            revives = stats?.revives ?? 0,
            zonesHeld = stats?.zonesHeld ?? 0,
            survivalTime = Time.time, // Match duration
            damageDealt = (int)(stats?.totalDamageDealt ?? 0),
            damageTaken = (int)(stats?.totalDamageTaken ?? 0),
            weaponUsed = inventory?.equippedWeapon ?? "None",
            matchWinner = agent.teamID == GetWinningTeam(),
            finalScore = CalculateFinalScore(stats)
        };
    }

    MatchResult CreateMatchResultFromSquadMate(SquadMateAgent agent)
    {
        AgentStats stats = agent.GetComponent<AgentStats>();
        HealthSystem health = agent.GetComponent<HealthSystem>();
        InventorySystem inventory = agent.GetComponent<InventorySystem>();

        return new MatchResult
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            agentName = agent.name,
            teamID = agent.tag.Contains("TeamA") ? "TeamA" : "TeamB",
            role = "SquadMate",
            kills = stats?.kills ?? 0,
            deaths = stats?.deaths ?? 0,
            kdr = stats?.GetKDR() ?? 0f,
            revives = stats?.revives ?? 0,
            zonesHeld = stats?.zonesHeld ?? 0,
            survivalTime = Time.time,
            damageDealt = (int)(stats?.totalDamageDealt ?? 0),
            damageTaken = (int)(stats?.totalDamageTaken ?? 0),
            weaponUsed = inventory?.equippedWeapon ?? "None",
            matchWinner = false, // Determine based on team performance
            finalScore = CalculateFinalScore(stats)
        };
    }

    TeamID GetWinningTeam()
    {
        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager != null)
        {
            return squadManager.teamAScore > squadManager.teamBScore ? TeamID.TeamA : TeamID.TeamB;
        }
        return TeamID.TeamA; // Default
    }

    float CalculateFinalScore(AgentStats stats)
    {
        if (stats == null) return 0f;

        float score = 0f;
        score += stats.kills * 10f;
        score += stats.revives * 5f;
        score += stats.zonesHeld * 3f;
        score -= stats.deaths * 5f;

        return Mathf.Max(0f, score);
    }

    void ExportToCSV(List<MatchResult> results)
    {
        StringBuilder sb = new StringBuilder();

        foreach (var result in results)
        {
            sb.AppendLine($"{result.timestamp},{result.agentName},{result.teamID},{result.role}," +
                         $"{result.kills},{result.deaths},{result.kdr:F2},{result.revives}," +
                         $"{result.zonesHeld},{result.survivalTime:F1},{result.damageDealt}," +
                         $"{result.damageTaken},{result.weaponUsed},{result.matchWinner},{result.finalScore:F1}");
        }

        File.AppendAllText(csvPath, sb.ToString());
        Debug.Log($"📄 CSV exported to: {csvPath}");
    }

    void ExportToJSON(List<MatchResult> results)
    {
        string json = JsonUtility.ToJson(new Serialization<MatchResult>(results), true);
        File.WriteAllText(jsonPath, json);
        Debug.Log($"📄 JSON exported to: {jsonPath}");
    }

    [System.Serializable]
    public class Serialization<T>
    {
        public List<T> items;
        public Serialization(List<T> items) { this.items = items; }
    }

    // Public method to manually trigger export
    [ContextMenu("Export Current Match")]
    public void ManualExport()
    {
        LogMatchResults();
    }

    // Method to be called when match ends
    public void OnMatchEnd()
    {
        if (autoExportOnMatchEnd)
        {
            LogMatchResults();
        }
    }

    void OnApplicationPause(bool pauseStatus)
    {
        if (pauseStatus && autoExportOnMatchEnd)
        {
            LogMatchResults();
        }
    }
}
