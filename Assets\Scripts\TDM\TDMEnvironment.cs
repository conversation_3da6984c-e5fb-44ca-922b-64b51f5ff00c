using UnityEngine;
using System.Collections.Generic;
using System.Collections;

/// <summary>
/// Team Deathmatch Environment Manager
/// Handles arena setup, loot spawning, and environmental systems for 5v5 TDM
/// </summary>
public class TDMEnvironment : MonoBehaviour
{
    [Header("🗺️ TDM Arena Configuration")]
    public GameObject tdmArenaPrefab;
    public Transform arenaRoot;

    [Header("🎯 Capture Zones")]
    public List<CaptureZone> captureZones = new List<CaptureZone>();
    public float zoneControlRadius = 5f;
    public float zoneControlTime = 10f;

    [Header("🔫 Weapon Spawns")]
    public Transform[] weaponSpawnPoints;
    public GameObject[] weaponPrefabs;
    public float weaponRespawnTime = 30f;

    [Header("💊 Health Spawns")]
    public Transform[] healthSpawnPoints;
    public GameObject[] healthPrefabs;
    public float healthRespawnTime = 20f;

    [Header("🛡️ Armor Spawns")]
    public Transform[] armorSpawnPoints;
    public GameObject[] armorPrefabs;
    public float armorRespawnTime = 45f;

    [Header("🎮 Environment Settings")]
    public bool enableDynamicLighting = true;
    public bool enableWeatherEffects = false;
    public float gravityMultiplier = 1f;

    private List<GameObject> spawnedItems = new List<GameObject>();
    private Dictionary<Transform, float> lastSpawnTimes = new Dictionary<Transform, float>();

    void Start()
    {
        InitializeArena();
        SetupSpawnSystems();
        StartCoroutine(ManageEnvironment());
    }

    void InitializeArena()
    {
        Debug.Log("🗺️ Initializing TDM Arena...");

        // Setup arena if not already present
        if (arenaRoot == null)
        {
            GameObject arena = GameObject.Find("Tdm") ?? GameObject.Find("TDM_Arena");
            if (arena != null)
            {
                arenaRoot = arena.transform;
                Debug.Log("✅ Found existing TDM arena");
            }
            else if (tdmArenaPrefab != null)
            {
                GameObject newArena = Instantiate(tdmArenaPrefab);
                newArena.name = "TDM_Arena";
                arenaRoot = newArena.transform;
                Debug.Log("✅ Created new TDM arena from prefab");
            }
            else
            {
                Debug.LogWarning("⚠️ No TDM arena found or prefab assigned!");
            }
        }

        // Setup NavMesh for the arena
        SetupNavMesh();

        // Initialize capture zones
        InitializeCaptureZones();

        Debug.Log("✅ TDM Arena initialized");
    }

    void SetupNavMesh()
    {
        if (arenaRoot == null) return;

        // Mark all static geometry for NavMesh baking
        MeshRenderer[] renderers = arenaRoot.GetComponentsInChildren<MeshRenderer>();
        foreach (MeshRenderer renderer in renderers)
        {
            if (renderer.gameObject.isStatic)
            {
                continue; // Already static
            }

            // Mark as static for navigation
            renderer.gameObject.isStatic = true;
        }

        Debug.Log($"✅ Marked {renderers.Length} objects as static for NavMesh");
    }

    void InitializeCaptureZones()
    {
        // Find existing capture zones or create them
        CaptureZone[] existingZones = FindObjectsOfType<CaptureZone>();
        captureZones.AddRange(existingZones);

        if (captureZones.Count == 0)
        {
            // Create default capture zones if none exist
            CreateDefaultCaptureZones();
        }

        Debug.Log($"✅ Initialized {captureZones.Count} capture zones");
    }

    void CreateDefaultCaptureZones()
    {
        Vector3[] defaultPositions = {
            new Vector3(0, 0, 0),      // Center
            new Vector3(-20, 0, 20),   // North West
            new Vector3(20, 0, 20),    // North East
            new Vector3(-20, 0, -20),  // South West
            new Vector3(20, 0, -20)    // South East
        };

        for (int i = 0; i < defaultPositions.Length; i++)
        {
            GameObject zoneObj = new GameObject($"CaptureZone_{i}");
            zoneObj.transform.position = defaultPositions[i];

            CaptureZone zone = zoneObj.AddComponent<CaptureZone>();
            zone.Initialize($"Zone {i + 1}", zoneControlRadius, zoneControlTime);

            captureZones.Add(zone);
        }
    }

    void SetupSpawnSystems()
    {
        Debug.Log("🎯 Setting up spawn systems...");

        // Initialize spawn timers with null checks
        if (weaponSpawnPoints != null)
        {
            foreach (Transform spawnPoint in weaponSpawnPoints)
            {
                if (spawnPoint != null)
                {
                    lastSpawnTimes[spawnPoint] = 0f;
                }
            }
        }

        if (healthSpawnPoints != null)
        {
            foreach (Transform spawnPoint in healthSpawnPoints)
            {
                if (spawnPoint != null)
                {
                    lastSpawnTimes[spawnPoint] = 0f;
                }
            }
        }

        if (armorSpawnPoints != null)
        {
            foreach (Transform spawnPoint in armorSpawnPoints)
            {
                if (spawnPoint != null)
                {
                    lastSpawnTimes[spawnPoint] = 0f;
                }
            }
        }

        // Initial spawn
        SpawnInitialItems();

        Debug.Log("✅ Spawn systems initialized");
    }

    void SpawnInitialItems()
    {
        // Spawn weapons with null checks
        if (weaponSpawnPoints != null)
        {
            foreach (Transform spawnPoint in weaponSpawnPoints)
            {
                if (spawnPoint != null)
                {
                    SpawnWeapon(spawnPoint);
                }
            }
        }

        // Spawn health items with null checks
        if (healthSpawnPoints != null)
        {
            foreach (Transform spawnPoint in healthSpawnPoints)
            {
                if (spawnPoint != null)
                {
                    SpawnHealthItem(spawnPoint);
                }
            }
        }

        // Spawn armor items with null checks
        if (armorSpawnPoints != null)
        {
            foreach (Transform spawnPoint in armorSpawnPoints)
            {
                if (spawnPoint != null)
                {
                    SpawnArmorItem(spawnPoint);
                }
            }
        }
    }

    IEnumerator ManageEnvironment()
    {
        while (true)
        {
            yield return new WaitForSeconds(1f);

            // Check for item respawns
            CheckWeaponRespawns();
            CheckHealthRespawns();
            CheckArmorRespawns();

            // Update capture zones
            UpdateCaptureZones();

            // Clean up destroyed items
            CleanupDestroyedItems();
        }
    }

    void CheckWeaponRespawns()
    {
        if (weaponSpawnPoints != null)
        {
            foreach (Transform spawnPoint in weaponSpawnPoints)
            {
                if (spawnPoint != null && ShouldRespawnItem(spawnPoint, weaponRespawnTime))
                {
                    SpawnWeapon(spawnPoint);
                }
            }
        }
    }

    void CheckHealthRespawns()
    {
        if (healthSpawnPoints != null)
        {
            foreach (Transform spawnPoint in healthSpawnPoints)
            {
                if (spawnPoint != null && ShouldRespawnItem(spawnPoint, healthRespawnTime))
                {
                    SpawnHealthItem(spawnPoint);
                }
            }
        }
    }

    void CheckArmorRespawns()
    {
        if (armorSpawnPoints != null)
        {
            foreach (Transform spawnPoint in armorSpawnPoints)
            {
                if (spawnPoint != null && ShouldRespawnItem(spawnPoint, armorRespawnTime))
                {
                    SpawnArmorItem(spawnPoint);
                }
            }
        }
    }

    bool ShouldRespawnItem(Transform spawnPoint, float respawnTime)
    {
        if (!lastSpawnTimes.ContainsKey(spawnPoint))
        {
            return true;
        }

        // Check if enough time has passed and no item exists at spawn point
        bool timeElapsed = Time.time - lastSpawnTimes[spawnPoint] >= respawnTime;
        bool noItemPresent = !IsItemAtSpawnPoint(spawnPoint);

        return timeElapsed && noItemPresent;
    }

    bool IsItemAtSpawnPoint(Transform spawnPoint)
    {
        Collider[] items = Physics.OverlapSphere(spawnPoint.position, 2f);
        foreach (Collider item in items)
        {
            if (item.GetComponent<LootItem>() != null ||
                item.GetComponent<WeaponPickup>() != null ||
                item.GetComponent<MedkitPickup>() != null)
            {
                return true;
            }
        }
        return false;
    }

    void SpawnWeapon(Transform spawnPoint)
    {
        if (weaponPrefabs.Length == 0) return;

        GameObject weaponPrefab = weaponPrefabs[Random.Range(0, weaponPrefabs.Length)];
        GameObject weapon = Instantiate(weaponPrefab, spawnPoint.position, spawnPoint.rotation);

        spawnedItems.Add(weapon);
        lastSpawnTimes[spawnPoint] = Time.time;

        Debug.Log($"🔫 Spawned weapon at {spawnPoint.name}");
    }

    void SpawnHealthItem(Transform spawnPoint)
    {
        if (healthPrefabs.Length == 0) return;

        GameObject healthPrefab = healthPrefabs[Random.Range(0, healthPrefabs.Length)];
        GameObject health = Instantiate(healthPrefab, spawnPoint.position, spawnPoint.rotation);

        spawnedItems.Add(health);
        lastSpawnTimes[spawnPoint] = Time.time;

        Debug.Log($"💊 Spawned health item at {spawnPoint.name}");
    }

    void SpawnArmorItem(Transform spawnPoint)
    {
        if (armorPrefabs.Length == 0) return;

        GameObject armorPrefab = armorPrefabs[Random.Range(0, armorPrefabs.Length)];
        GameObject armor = Instantiate(armorPrefab, spawnPoint.position, spawnPoint.rotation);

        spawnedItems.Add(armor);
        lastSpawnTimes[spawnPoint] = Time.time;

        Debug.Log($"🛡️ Spawned armor item at {spawnPoint.name}");
    }

    void UpdateCaptureZones()
    {
        foreach (CaptureZone zone in captureZones)
        {
            if (zone != null)
            {
                zone.UpdateZone();
            }
        }
    }

    void CleanupDestroyedItems()
    {
        spawnedItems.RemoveAll(item => item == null);
    }

    public Transform GetNearestCaptureZone(Vector3 position)
    {
        if (captureZones.Count == 0) return null;

        Transform nearest = captureZones[0].transform;
        float minDistance = Vector3.Distance(position, nearest.position);

        for (int i = 1; i < captureZones.Count; i++)
        {
            if (captureZones[i] != null)
            {
                float distance = Vector3.Distance(position, captureZones[i].transform.position);
                if (distance < minDistance)
                {
                    minDistance = distance;
                    nearest = captureZones[i].transform;
                }
            }
        }

        return nearest;
    }

    public List<Transform> GetAllCaptureZones()
    {
        List<Transform> zones = new List<Transform>();
        foreach (CaptureZone zone in captureZones)
        {
            if (zone != null)
            {
                zones.Add(zone.transform);
            }
        }
        return zones;
    }

    void OnDrawGizmosSelected()
    {
        // Draw weapon spawn points
        Gizmos.color = Color.red;
        foreach (Transform spawn in weaponSpawnPoints)
        {
            if (spawn != null)
            {
                Gizmos.DrawWireSphere(spawn.position, 1f);
            }
        }

        // Draw health spawn points
        Gizmos.color = Color.green;
        foreach (Transform spawn in healthSpawnPoints)
        {
            if (spawn != null)
            {
                Gizmos.DrawWireSphere(spawn.position, 1f);
            }
        }

        // Draw armor spawn points
        Gizmos.color = Color.blue;
        foreach (Transform spawn in armorSpawnPoints)
        {
            if (spawn != null)
            {
                Gizmos.DrawWireSphere(spawn.position, 1f);
            }
        }
    }
}
