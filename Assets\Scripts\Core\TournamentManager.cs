using UnityEngine;
using System.Collections.Generic;
using System.Linq;
using System;

/// <summary>
/// 🎓 Tournament Bracket + ELO Tracker
/// Manages competitive rankings and tournament-style matches between AI agents
/// </summary>
public class TournamentManager : MonoBehaviour
{
    [Header("🏆 Tournament Settings")]
    public int maxTournamentRounds = 8;
    public float eloKFactor = 32f;
    public float initialELO = 1000f;
    public bool autoStartNextMatch = true;
    public float matchCooldownTime = 5f;

    [Header("📊 Leaderboard Display")]
    public bool showLeaderboardInConsole = true;
    public int topPlayersToShow = 10;

    [System.Serializable]
    public class AgentScore
    {
        public string name;
        public int wins = 0;
        public int losses = 0;
        public int draws = 0;
        public float elo = 1000f;
        public int matchesPlayed = 0;
        public float averageKDR = 0f;
        public int totalKills = 0;
        public int totalDeaths = 0;
        public DateTime lastMatchTime;
        public string currentRank = "Unranked";

        public float WinRate => matchesPlayed > 0 ? (float)wins / matchesPlayed * 100f : 0f;
        public float KDR => totalDeaths > 0 ? (float)totalKills / totalDeaths : totalKills;
    }

    [System.Serializable]
    public class MatchRecord
    {
        public string timestamp;
        public string winner;
        public string loser;
        public float winnerELOBefore;
        public float winnerELOAfter;
        public float loserELOBefore;
        public float loserELOAfter;
        public string matchType;
    }

    private Dictionary<string, AgentScore> leaderboard = new Dictionary<string, AgentScore>();
    private List<MatchRecord> matchHistory = new List<MatchRecord>();
    private int currentTournamentRound = 0;
    private bool tournamentActive = false;

    // ELO Rank Thresholds
    private readonly Dictionary<string, float> rankThresholds = new Dictionary<string, float>
    {
        { "Bronze", 800f },
        { "Silver", 1000f },
        { "Gold", 1200f },
        { "Platinum", 1400f },
        { "Diamond", 1600f },
        { "Master", 1800f },
        { "Grandmaster", 2000f }
    };

    void Start()
    {
        Debug.Log("🏆 Tournament Manager initialized");
        
        // Initialize existing agents
        InitializeExistingAgents();
        
        // Subscribe to match events
        SquadManager squadManager = FindObjectOfType<SquadManager>();
        if (squadManager != null)
        {
            Debug.Log("🔗 Connected to SquadManager for tournament tracking");
        }
    }

    void InitializeExistingAgents()
    {
        VictorAgent[] agents = FindObjectsOfType<VictorAgent>();
        foreach (var agent in agents)
        {
            RegisterAgent(agent.name);
        }
        
        SquadMateAgent[] squadAgents = FindObjectsOfType<SquadMateAgent>();
        foreach (var agent in squadAgents)
        {
            RegisterAgent(agent.name);
        }
        
        Debug.Log($"📝 Registered {leaderboard.Count} agents for tournament");
    }

    public void RegisterAgent(string agentName)
    {
        if (!leaderboard.ContainsKey(agentName))
        {
            leaderboard[agentName] = new AgentScore 
            { 
                name = agentName, 
                elo = initialELO,
                lastMatchTime = DateTime.Now,
                currentRank = GetRankFromELO(initialELO)
            };
            Debug.Log($"🆕 Registered new agent: {agentName} (ELO: {initialELO})");
        }
    }

    public void RegisterMatchResult(string winnerName, string loserName, bool isDraw = false)
    {
        // Ensure both agents are registered
        RegisterAgent(winnerName);
        RegisterAgent(loserName);

        AgentScore winner = leaderboard[winnerName];
        AgentScore loser = leaderboard[loserName];

        // Store ELO before changes
        float winnerELOBefore = winner.elo;
        float loserELOBefore = loser.elo;

        if (isDraw)
        {
            // Handle draw
            winner.draws++;
            loser.draws++;
            
            // Small ELO adjustment for draws
            float expectedWinner = CalculateExpectedScore(winner.elo, loser.elo);
            winner.elo += eloKFactor * (0.5f - expectedWinner);
            loser.elo += eloKFactor * (0.5f - (1f - expectedWinner));
        }
        else
        {
            // Handle win/loss
            winner.wins++;
            loser.losses++;

            // Calculate ELO changes
            float expectedWinner = CalculateExpectedScore(winner.elo, loser.elo);
            float expectedLoser = 1f - expectedWinner;

            winner.elo += eloKFactor * (1f - expectedWinner);
            loser.elo += eloKFactor * (0f - expectedLoser);
        }

        // Update match counts and timestamps
        winner.matchesPlayed++;
        loser.matchesPlayed++;
        winner.lastMatchTime = DateTime.Now;
        loser.lastMatchTime = DateTime.Now;

        // Update ranks
        winner.currentRank = GetRankFromELO(winner.elo);
        loser.currentRank = GetRankFromELO(loser.elo);

        // Record match history
        MatchRecord record = new MatchRecord
        {
            timestamp = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"),
            winner = winnerName,
            loser = loserName,
            winnerELOBefore = winnerELOBefore,
            winnerELOAfter = winner.elo,
            loserELOBefore = loserELOBefore,
            loserELOAfter = loser.elo,
            matchType = isDraw ? "Draw" : "Victory"
        };
        matchHistory.Add(record);

        Debug.Log($"🏆 Match Result: {winnerName} ({winner.elo:F1}) vs {loserName} ({loser.elo:F1})");
        
        if (showLeaderboardInConsole)
        {
            PrintTopPlayers(5);
        }
    }

    public void UpdateAgentStats(string agentName, int kills, int deaths)
    {
        if (leaderboard.ContainsKey(agentName))
        {
            AgentScore agent = leaderboard[agentName];
            agent.totalKills += kills;
            agent.totalDeaths += deaths;
            agent.averageKDR = agent.KDR;
        }
    }

    float CalculateExpectedScore(float ratingA, float ratingB)
    {
        return 1f / (1f + Mathf.Pow(10f, (ratingB - ratingA) / 400f));
    }

    string GetRankFromELO(float elo)
    {
        foreach (var rank in rankThresholds.OrderByDescending(x => x.Value))
        {
            if (elo >= rank.Value)
                return rank.Key;
        }
        return "Unranked";
    }

    public void PrintLeaderboard()
    {
        PrintTopPlayers(topPlayersToShow);
    }

    public void PrintTopPlayers(int count)
    {
        var sortedPlayers = leaderboard.Values
            .OrderByDescending(x => x.elo)
            .Take(count)
            .ToList();

        Debug.Log("🏆 === TOURNAMENT LEADERBOARD ===");
        for (int i = 0; i < sortedPlayers.Count; i++)
        {
            var player = sortedPlayers[i];
            Debug.Log($"{i + 1}. {player.name} | {player.currentRank} | " +
                     $"ELO: {player.elo:F1} | W:{player.wins} L:{player.losses} D:{player.draws} | " +
                     $"KDR: {player.KDR:F2} | WR: {player.WinRate:F1}%");
        }
        Debug.Log("================================");
    }

    public AgentScore GetAgentScore(string agentName)
    {
        return leaderboard.ContainsKey(agentName) ? leaderboard[agentName] : null;
    }

    public List<AgentScore> GetTopPlayers(int count)
    {
        return leaderboard.Values
            .OrderByDescending(x => x.elo)
            .Take(count)
            .ToList();
    }

    public void StartTournament()
    {
        if (leaderboard.Count < 2)
        {
            Debug.LogWarning("⚠️ Need at least 2 agents to start tournament");
            return;
        }

        tournamentActive = true;
        currentTournamentRound = 1;
        Debug.Log($"🚀 Tournament started with {leaderboard.Count} participants!");
        
        if (autoStartNextMatch)
        {
            Invoke(nameof(TriggerNextMatch), matchCooldownTime);
        }
    }

    void TriggerNextMatch()
    {
        if (tournamentActive && currentTournamentRound <= maxTournamentRounds)
        {
            Debug.Log($"⚔️ Tournament Round {currentTournamentRound} starting...");
            currentTournamentRound++;
            
            // Trigger next match after cooldown
            if (currentTournamentRound <= maxTournamentRounds)
            {
                Invoke(nameof(TriggerNextMatch), matchCooldownTime);
            }
            else
            {
                EndTournament();
            }
        }
    }

    void EndTournament()
    {
        tournamentActive = false;
        Debug.Log("🏁 Tournament completed!");
        PrintLeaderboard();
        
        var champion = leaderboard.Values.OrderByDescending(x => x.elo).First();
        Debug.Log($"👑 Tournament Champion: {champion.name} (ELO: {champion.elo:F1})");
    }

    // Context menu for testing
    [ContextMenu("Print Leaderboard")]
    void DebugPrintLeaderboard()
    {
        PrintLeaderboard();
    }

    [ContextMenu("Start Tournament")]
    void DebugStartTournament()
    {
        StartTournament();
    }

    [ContextMenu("Simulate Random Match")]
    void DebugSimulateMatch()
    {
        var agents = leaderboard.Keys.ToList();
        if (agents.Count >= 2)
        {
            string winner = agents[UnityEngine.Random.Range(0, agents.Count)];
            string loser = agents[UnityEngine.Random.Range(0, agents.Count)];
            if (winner != loser)
            {
                RegisterMatchResult(winner, loser);
            }
        }
    }
}
