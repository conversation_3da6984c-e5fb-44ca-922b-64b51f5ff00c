using UnityEngine;
using UnityEditor;
using Unity.AI.Navigation;
using UnityEngine.AI;

/// <summary>
/// Helper for NavMesh operations in the arena
/// </summary>
public class NavMeshHelper : EditorWindow
{
    [MenuItem("SquadMate AI/🗺️ NavMesh Helper")]
    public static void ShowWindow()
    {
        GetWindow<NavMeshHelper>("NavMesh Helper");
    }

    void OnGUI()
    {
        GUILayout.Label("🗺️ NavMesh Helper", EditorStyles.boldLabel);
        GUILayout.Space(10);

        if (GUILayout.Button("🔧 Setup NavMesh Surface"))
        {
            SetupNavMeshSurface();
        }

        if (GUILayout.Button("🏗️ Bake NavMesh"))
        {
            BakeNavMesh();
        }

        if (GUILayout.Button("🧹 Clear NavMesh"))
        {
            ClearNavMesh();
        }

        GUILayout.Space(10);
        GUILayout.Label("NavMesh Status:", EditorStyles.boldLabel);

        NavMeshSurface surface = FindObjectOfType<NavMeshSurface>();
        if (surface != null)
        {
            GUILayout.Label("✅ NavMeshSurface found");
            GUILayout.Label($"   Area: {surface.collectObjects}");
            GUILayout.Label($"   Geometry: {surface.useGeometry}");
        }
        else
        {
            GUILayout.Label("❌ No NavMeshSurface found");
        }

        // Check if NavMesh data exists
        var triangulation = UnityEngine.AI.NavMesh.CalculateTriangulation();
        if (triangulation.vertices.Length > 0)
        {
            GUILayout.Label($"✅ NavMesh baked ({triangulation.vertices.Length} vertices)");
        }
        else
        {
            GUILayout.Label("❌ NavMesh not baked");
        }
    }

    void SetupNavMeshSurface()
    {
        // Find arena root
        GameObject arena = GameObject.Find("PUBG_TDM_Arena") ?? GameObject.Find("Tdm") ?? GameObject.Find("TDM_Arena");
        if (arena == null)
        {
            Debug.LogError("❌ No arena found! Generate arena first.");
            return;
        }

        // Check if NavMeshSurface already exists
        NavMeshSurface existing = arena.GetComponent<NavMeshSurface>();
        if (existing != null)
        {
            Debug.Log("✅ NavMeshSurface already exists");
            return;
        }

        // Add NavMeshSurface component
        NavMeshSurface surface = arena.AddComponent<NavMeshSurface>();

        // Configure settings
        surface.collectObjects = CollectObjects.Children;
        surface.useGeometry = UnityEngine.AI.NavMeshCollectGeometry.RenderMeshes;
        surface.layerMask = -1; // All layers
        surface.overrideVoxelSize = false;
        surface.overrideTileSize = false;

        Debug.Log("✅ NavMeshSurface added and configured");
        Selection.activeGameObject = arena;
    }

    void BakeNavMesh()
    {
        NavMeshSurface surface = FindObjectOfType<NavMeshSurface>();
        if (surface == null)
        {
            Debug.LogError("❌ No NavMeshSurface found! Setup NavMeshSurface first.");
            return;
        }

        Debug.Log("🏗️ Baking NavMesh...");
        surface.BuildNavMesh();

        // Verify baking
        var triangulation = UnityEngine.AI.NavMesh.CalculateTriangulation();
        if (triangulation.vertices.Length > 0)
        {
            Debug.Log($"✅ NavMesh baked successfully! ({triangulation.vertices.Length} vertices)");
        }
        else
        {
            Debug.LogError("❌ NavMesh baking failed!");
        }
    }

    void ClearNavMesh()
    {
        NavMeshSurface surface = FindObjectOfType<NavMeshSurface>();
        if (surface != null)
        {
            surface.RemoveData();
            Debug.Log("🧹 NavMesh data cleared");
        }
        else
        {
            Debug.Log("⚠️ No NavMeshSurface found to clear");
        }
    }

    [MenuItem("SquadMate AI/🚀 Quick Arena Setup")]
    public static void QuickArenaSetup()
    {
        Debug.Log("🚀 Starting quick arena setup...");

        // Step 1: Generate arena
        PUBGArenaGenerator generator = GetWindow<PUBGArenaGenerator>();
        generator.Close();

        // Find the generator and call its method
        var generatorType = typeof(PUBGArenaGenerator);
        var method = generatorType.GetMethod("GeneratePUBGArena",
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);

        if (method != null)
        {
            var instance = ScriptableObject.CreateInstance<PUBGArenaGenerator>();
            method.Invoke(instance, null);

            // Step 2: Setup NavMesh
            EditorApplication.delayCall += () =>
            {
                var helper = GetWindow<NavMeshHelper>();
                helper.SetupNavMeshSurface();
                helper.BakeNavMesh();
                helper.Close();

                Debug.Log("🎉 Quick arena setup complete!");
            };
        }
    }
}
